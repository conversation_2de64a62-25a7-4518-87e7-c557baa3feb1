<?php
// +----------------------------------------------------------------------
// | Qyapp
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace pass\driver;

use app\model\user\User;
use pass\Driver;
use pass\exception\AccessTokenException;
use pass\exception\UserDataException;
use pass\interfaces\User as InterfacesUser;
use pass\traits\TraitsMobileLogin;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\Exception;
use think\exception\ClassNotFoundException;
use think\facade\App;
use think\facade\Request;

class Qyapp extends Driver
{
    use TraitsMobileLogin;

    /**
     * 会员uid字段名
     * @var string
     */
    protected string $uidField = 'id';

    /**
     * 登陆令牌
     * @var null|string
     */
    protected ?string $accessToken = null;

    /**
     * 项目ID
     * @var int
     */
    protected int $projectId = 13;

    /**
     * 获取用户信息
     * @param int|string $identifier 手机号|邮箱|用户名
     * @param null|string $password 密码，改值不为空时进行密码验证，否则忽略
     * @param bool $auto 判断$identifier是属于什么方式，默认自动
     * @return InterfacesUser
     * @throws UserDataException
     */
    public function getUserInfo($identifier, string $password = null, $auto = true): InterfacesUser
    {
        try {
            return User::getUserInfo($identifier, $password, $auto);
        } catch (UserDataException $e) {
            $this->error = $e->getMessage() ?: '该用户不存在';
            throw new UserDataException($this->error, 10002);
        }
    }

    /**
     * 初始化现有登录信息
     * @return Qyapp
     * @throws UserDataException
     */
    public function loginStatusInit(): static
    {
        if (!empty($this->accessToken)) {
            try {
                $data = $this->getAccessTokenUser($this->accessToken);
            } catch (\Exception $e) {
            }
            if (!empty($data)) {
                $this->data($data);
            }
        }
        return $this;
    }

    /**
     * 手机号密码登陆
     * @param string $mobile 移动号码
     * @param string $password 账号密码
     * @return bool
     * @throws UserDataException
     * @throws AccessTokenException
     */
    public function mobilePasswordLogin($mobile, $password): bool
    {
        if (empty($mobile) || empty($password)) {
            $this->error = '账号或者密码不能为空';
            $this->errorCode = 10008;
            return false;
        }
        // 获取用户资料
        $user = $this->getUserInfo($mobile, $password, 'mobile');
        if (empty($user)) {
            $this->errorCode = 10002;
            return false;
        }
        // 是否禁止
        if (!$user['status']) {
            $this->error = '账号禁止访问';
            $this->errorCode = 10013;
            return false;
        }
        $this->data($user);
        // 注册登录状态
        return $this->registerLogin();
    }

    /**
     * 微信openid登录
     * @param string $openid
     * @return bool
     * @throws AccessTokenException
     * @throws UserDataException
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function wxOpenidLogin(string $openid): bool
    {
        $userAuthorize = User::where('wx_openid', $openid)->find();
        if (empty($userAuthorize)) {
            $this->errorCode = 10009;
            return false;
        }
        $user = $userAuthorize->user;
        if (empty($user)) {
            $this->errorCode = 10002;
            return false;
        }
        $this->data($user);
        // 注册登录状态
        return $this->registerLogin();
    }

    /**
     * 手机号登陆-验证码方式
     * @param string $mobile 移动号码
     * @param string $code 短信验证码
     * @param callable $callback 闭包验证
     * @param string $spread
     * @return bool
     * @throws AccessTokenException
     * @throws UserDataException|Exception
     */
    public function mobileLogin($mobile, $code, callable $callback, string $spread = ''): bool
    {
        if (empty($mobile) || empty($code)) {
            $this->error = '手机号或者验证码不能为空';
            $this->errorCode = 10008;
            return false;
        }
        if (is_callable($callback)) {
            try {
                $result = call_user_func_array($callback, [$mobile, $code]);
            } catch (\Throwable $e) {
                $result = false;
            }
        } else {
            $result = false;
        }
        if (true !== $result) {
            $this->error = '短信验证码错误';
            $this->errorCode = 10003;
            return false;
        }
        try {
            $user = $this->getUserInfo($mobile, null, 'mobile');
        } catch (UserDataException $e) {
            // 快速注册账号
            $data = [
                'mobile' => $mobile,
                'spread' => $spread,
                'spread_uid' => 0,
            ];
            $user = User::createByFront($data);
        }
        if (empty($user)) {
            $this->errorCode = 10002;
            return false;
        }
        // 是否禁止
        if (!$user['status']) {
            $this->error = '账号禁止访问';
            $this->errorCode = 10013;
            return false;
        }
        $this->data($user);
        // 注册登录状态
        return $this->registerLogin();
    }

    /**
     * 账号登录
     * @param string|integer $identifier 用户名或者用户ID
     * @param string $password 明文密码
     * @return bool
     */
    public function login($identifier, $password): bool
    {
        $this->error = '关闭账号登录';
        $this->errorCode = 10008;
        return false;
    }

    /**
     * 注销登录状态
     * @return bool
     * @throws AccessTokenException
     */
    public function logout(): bool
    {
        $accessInfo = $this->getAceessTokenInfo($this->accessToken);
        $this->clearUuid();
        if (!empty($accessInfo)) {
            try {
                // 删除token
                App::make(\rpc\contract\member\AccessTokenInterface::class)->delete($accessInfo['token']);
            } catch (ClassNotFoundException $e) {
                // 不支持内置rpc接口
            }
        }
        return true;
    }

    /**
     * 注册登录状态
     * @param null $relationToken 关联的令牌对象
     * @return bool
     * @throws AccessTokenException
     * @throws UserDataException
     */
    public function registerLogin($relationToken = null): bool
    {
        if (!$this->uid()) {
            $this->errorCode = 10010;
            return false;
        }
        // 30天有效期
        $this->remember(86400 * 30);
        // 生成token
        $accesssToken = $this->createAccessToken();
        if (empty($accesssToken)) {
            $this->errorCode = 10011;
            return false;
        }
        // 进行access关联
        if (!empty($relationToken)) {
            try {
                $rpcAccessToken = App::make(\rpc\contract\member\AccessTokenInterface::class);
                $relationToken = $rpcAccessToken->info($relationToken);
                if (!empty($relationToken)) {
                    $rpcAccessToken->relationToken($accesssToken['token'], $relationToken['id']);
                }
            } catch (ClassNotFoundException $e) {
                // 不支持内置rpc接口
            }
        }
        // 更新登录信息
        $this->data()->currentLogin();
        // 设置token
        $this->accessToken = $accesssToken['token'];
        return true;
    }

    /**
     * 设置获取access_token
     * @param null $accessToken 为true时自动获取
     * @return $this|bool|null
     * @throws UserDataException
     */
    public function accessToken($accessToken = null)
    {
        // 自动获取accesstoken
        if (true === $accessToken) {
            $request = Request::instance();
            $accessToken = $request->param('access_token', '', 'trim');
        }
        if (is_null($accessToken)) {
            return $this->accessToken;
        } elseif (is_string($accessToken) && empty($accessToken)) {
            return false;
        }
        $this->accessToken = $accessToken;
        // 初始化
        $this->loginStatusInit();
        return $this;
    }

    /**
     * 前台浏览器cookie记录登录标识
     * @return string
     */
    public function getUuidKey(): string
    {
        return 'QYID';
    }

    /**
     * 是否保持唯一登陆
     * @return bool
     */
    public function isLoginOnly(): bool
    {
        return true;
    }
}
