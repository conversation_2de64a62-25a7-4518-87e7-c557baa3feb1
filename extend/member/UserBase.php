<?php
// +----------------------------------------------------------------------
// | UserBase 会员基础
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace member;

use app\model\user\User;
use app\traits\PassDriverTraits;
use pass\Driver as PassDriver;
use pass\driver\Qyapp;
use pass\exception\UserDataException;
use pass\interfaces\User as InterfacesUser;
use pass\objects\User as PassUserObject;
use think\exception\HttpResponseException;
use think\facade\Request;

abstract class UserBase
{
    use PassDriverTraits;

    /**
     * 登录驱动
     * @var PassDriver|Qyapp
     */
    protected PassDriver | Qyapp $passService;

    /**
     * 登录令牌
     * @var string
     */
    protected string $accessToken = '';

    /**
     * 会员对象信息
     * @var PassUserObject|InterfacesUser
     */
    protected PassUserObject | InterfacesUser $user;

    /**
     * 初始化
     * @throws UserDataException
     */
    public function __construct()
    {
        // 实例化会员驱动
        $this->passService = User::passService($this->initDriver());
        // 是否有登陆令牌
        if (Request::has('access_token')) {
            $this->accessToken = Request::param('access_token', '', 'trim');
            // 有传递token，尝试注入token，并完成初始化获取会员信息
            $this->passService->accessToken($this->accessToken);
        } else {
            $this->passService->loginStatusInit();
            $this->accessToken = $this->passService->accessToken(null);
        }
        $this->init();
    }

    /**
     * 初始化
     * @throws UserDataException
     */
    protected function init()
    {
        $this->checkAuth();
        $this->initUser();
    }

    /**
     * 获取当前登录UID
     * @return int
     */
    protected function uid(): int
    {
        return $this->passService->uid();
    }

    /**
     * 获取当前登录UID
     * @return string
     */
    protected function username(): string
    {
        return $this->passService->username();
    }

    /**
     * 初始化当前登录用户信息
     * @throws UserDataException
     */
    protected function initUser()
    {
        $this->user = $this->passService->data();
    }

    /**
     * 是否登录
     * @return bool
     */
    protected function isLogin(): bool
    {
        if (empty($this->passService)) {
            return false;
        }
        return $this->passService->isLogin();
    }

    /**
     * 检查权限
     * @return void
     */
    protected function checkAuth()
    {
        if (!$this->isLogin()) {
            $this->notLogedInReturn();
        }
    }

    /**
     * 未登录返回处理
     * @return void
     */
    protected function notLogedInReturn()
    {
        if (empty($this->accessToken)) {
            $response = json(['status' => 0, 'error_code' => 20000, 'msg' => '请先登录']);
        } else {
            $accessToken = Request::param('access_token', null, 'trim');
            if (empty($accessToken)) {
                $response = json(['status' => 0, 'error_code' => 20000, 'msg' => '请先登录']);
            } else {
                $response = json(['status' => 0, 'error_code' => 20002, 'msg' => '登录信息已过期']);
            }
        }
        throw new HttpResponseException($response);
    }
}
