<?php
// +----------------------------------------------------------------------
// | 配置
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

use think\facade\App;
use think\facade\Env;
use think\swoole\websocket\socketio\Handler;

return [
    // http服务配置
    'http' => [
        'enable' => true,
        // 监听地址
        'host' => '0.0.0.0',
        // 监听端口
        'port' => 8080,
        // 工作进程
        'worker_num' => 1,
    ],
    'rpc' => [
        'server' => [
            'enable' => true,
            'host' => '0.0.0.0',
            'port' => 9090,
            'worker_num' => 1,
            'middleware' => [],
            'services' => [
                \app\rpc\CesuanOrder::class,
                \app\rpc\User::class,
            ],
        ],
        'client' => [
            // 总后台
            'nadm' => [
                'host' => Env::get('rpc_admin.host', '127.0.0.1'),
                'port' => Env::get('rpc_admin.port', 9050),
                'max_active' => Env::get('rpc_admin.max_active', 20),
                'max_wait_time' => 1,
            ],
            // 上传服务
            'upload' => [
                'host' => Env::get('rpc_upload.host', '127.0.0.1'),
                'port' => Env::get('rpc_upload.port', 9100),
                'max_active' => Env::get('rpc_upload.max_active', 20),
                'max_wait_time' => 1,
            ],
            // 会员服务
            'member' => [
                'host' => Env::get('rpc_member.host', '127.0.0.1'),
                'port' => Env::get('rpc_member.port', 9110),
                'max_active' => Env::get('rpc_member.max_active', 10),
                'max_wait_time' => 1,
            ],
            // 接口服务
            'api' => [
                'host' => Env::get('rpc_api.host', '127.0.0.1'),
                'port' => Env::get('rpc_api.port', 9220),
                'max_active' => Env::get('rpc_api.max_active', 10),
                'max_wait_time' => 1,
            ],
            // 支付服务
            'pay' => [
                'host' => Env::get('rpc_pay.host', '127.0.0.1'),
                'port' => Env::get('rpc_pay.port', 9060),
                'max_active' => Env::get('rpc_pay.max_active', 10),
                'max_wait_time' => 1,
            ],
            // 测算服务
            'cesuan' => [
                'host' => Env::get('rpc_cesuan.host', '127.0.0.1'),
                'port' => Env::get('rpc_cesuan.port', 9200),
                'max_active' => Env::get('rpc_cesuan.max_active', 10),
                'max_wait_time' => 1,
            ],
        ],
    ],
    // 队列
    'queue' => [
        'enable' => Env::get('queue.enable', false),
        'workers' => [
            'qyapp@default' => [
                'worker_num' => 1,
                'delay' => 3,
                'timeout' => 10,
                'sleep' => 3,
                'tries' => 3,
            ],
            'qyapp_longtime@longtime' => [
                'worker_num' => 1,
                'delay' => 3,
                'timeout' => 120,
                'sleep' => 3,
                'tries' => 3,
            ],
        ],
    ],
    // websocket支持
    'websocket' => [
        'enable' => false,
        'handler' => Handler::class,
        'ping_interval' => 25000,
        'ping_timeout' => 60000,
        'room' => [
            'type' => 'table',
            'table' => [
                'room_rows' => 4096,
                'room_size' => 2048,
                'client_rows' => 8192,
                'client_size' => 2048,
            ],
            'redis' => [
                'host' => '127.0.0.1',
                'port' => 6379,
                'max_active' => 3,
                'max_wait_time' => 5,
            ],
        ],
        'listen' => [],
        'subscribe' => [],
    ],
    // 热更新配置
    'hot_update' => [
        // 是否启用热更新
        'enable' => Env::get('swoole.hot_update', false),
        // 监控文件类型
        'name' => ['*.php', '*.env'],
        // 监控访问
        'include' => [
            App::getRootPath() . 'view' . DIRECTORY_SEPARATOR,
            App::getBasePath(),
        ],
        'exclude' => [],
    ],
    // 连接池
    'pool' => [
        'db' => [
            'enable' => true,
            'max_active' => Env::get('pool_db.max_active', 10),
            'max_wait_time' => Env::get('pool_db.max_wait_time', 1),
            'max_idle_time' => Env::get('pool_db.max_idle_time', 20),
        ],
        'cache' => [
            'enable' => true,
            'max_active' => Env::get('pool_cache.max_active', 20),
            'max_wait_time' => Env::get('pool_cache.max_wait_time', 1),
            'max_idle_time' => Env::get('pool_cache.max_idle_time', 20),
        ],
    ],
    'tables' => [],
    // 每个worker里需要预加载以共用的实例
    'concretes' => [],
    // 重置器
    'resetters' => [],
    // 每次请求前需要清空的实例
    'instances' => [],
    // 每次请求前需要重新执行的服务
    'services' => [],
];
