<?php
// +----------------------------------------------------------------------
// | 缓存设置
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

use think\facade\Env;

return [
    // 默认缓存驱动
    'default' => Env::get('cache.driver', 'redis'),
    // 缓存连接方式配置
    'stores' => [
        // 缓存
        'redis' => [
            'type' => 'Redis',
            'host' => Env::get('redis.host', '127.0.0.1'),
            'port' => 6379,
            'prefix' => 'qyapp.',
        ],
    ],
];
