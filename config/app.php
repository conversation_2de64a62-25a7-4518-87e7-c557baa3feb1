<?php
// +----------------------------------------------------------------------
// | 应用设置
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

use think\facade\Env;

return [
    // 应用名称
    'app_name' => 'qyapp',
    // 应用地址
    'app_host' => Env::get('app.host', ''),
    // 应用的命名空间
    'app_namespace' => '',
    // 是否启用路由
    'with_route' => true,
    // 默认时区
    'default_timezone' => 'Asia/Shanghai',
    // 异常页面的模板文件
    'exception_tmpl' => app()->getThinkPath() . 'tpl/think_exception.tpl',
    // HttpException异常模板
    'http_exception_template' => [
        404 => app()->getRootPath() . 'view/common/404.php',
        500 => app()->getRootPath() . 'view/common/500.php',
    ],
    // 默认跳转页面对应的模板文件
    'dispatch_success_tmpl' => app()->getRootPath() . 'view/common/success.php',
    'dispatch_error_tmpl' => app()->getRootPath() . 'view/common/error.php',
    // 错误显示信息,非调试模式有效
    'error_message' => '页面错误！请稍后再试～',
    // 显示错误信息
    'show_error_msg' => Env::get('app.show_error_msg', false),
    // 默认输出类型
    'default_return_type' => Env::get('app.default_return_type', 'html'),
    // 默认AJAX 数据返回格式,可选json xml ...
    'default_ajax_return' => Env::get('app.default_ajax_return', 'json'),
];
