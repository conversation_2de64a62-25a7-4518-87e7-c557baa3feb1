<?php
// +----------------------------------------------------------------------
// | 设置
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

use think\facade\Env;

return [
    // 默认队列
    'default' => 'default',
    // 设置延迟失败任务的时间
    'delay' => 3,
    // 设置内存限制，以兆字节为单位
    'memory' => 128,
    // 设置子进程可以运行的秒数
    'timeout' => 60,
    // 设置没有可用任务时休眠的秒数
    'sleep' => 3,
    // 设置在记录失败之前尝试执行的次数
    'tries' => 3,
    // 驱动列表
    'connections' => [
        // 默认队列信息
        'default' => [
            'queue' => 'qyapp',
            'type' => Env::get('queue.type', 'redis'),
            'host' => Env::get('queue.host', '127.0.0.1'),
            'port' => Env::get('queue.port', 6379),
            'password' => '',
            'select' => 0,
            'timeout' => 10,
            'persistent' => false,
        ],
        // 耗时任务
        'longtime' => [
            'queue' => 'qyapp_longtime',
            'type' => Env::get('queue.type', 'redis'),
            'host' => Env::get('queue.host', '127.0.0.1'),
            'port' => Env::get('queue.port', 6379),
            'password' => '',
            'select' => 0,
            'timeout' => 120,
            'persistent' => false,
        ],
    ],
];
