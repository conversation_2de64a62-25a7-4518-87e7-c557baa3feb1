# 开启调试模式
app_debug = true


# 基础
[base]
# 加密秘钥
authcode = G6XGMQIfouScBJML


# 默认队列
[queue]
enable = false
type = redis
host = redis-server
port = 6379


# 接口
[api]
mode = 2
dir = api


# 缓存配置
[cache]
driver = redis


# 缓存配置
[redis]
host = redis-server


# rpc后台
[rpc_admin]
host = think-admin
port = 9050
max_active = 20


# 上传服务
[rpc_upload]
host = think-upload
port = 9100
max_active = 20


# 会员服务
[rpc_member]
host = think-member
port = 9110
max_active = 10


# 接口服务
[rpc_api]
host = think-api
port = 9220
max_active = 10


# 支付服务
[rpc_pay]
host = think-pay
port = 9060
max_active = 100


# 测算服务
[rpc_cesuan]
host = think-cesuan
port = 9200
max_active = 100


# 连接池-数据库
[pool_db]
max_active = 5


# 连接池-缓存
[pool_cache]
max_active = 10


# 日志
[log]
channel = file


# 数据库配置
[database]
driver = default
hostname = *************
database = qyapp
username = dev
password = !@#qwe123
prefix = bw_

