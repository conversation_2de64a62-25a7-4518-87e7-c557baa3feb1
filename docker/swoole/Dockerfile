##############################################################################################################
# 本镜像构建的是 think-qyapp 的镜像
# 编译命令
#   版本规则：年.月.日.第几次
#   docker build -t registry.cn-hangzhou.aliyuncs.com/projecta/think-qyapp:latest -f ./docker/swoole/Dockerfile .
##############################################################################################################

FROM registry.cn-hangzhou.aliyuncs.com/basicsa/ubuntu-php82-swoole:v1.0

ADD docker/swoole/supervisord.conf /etc/supervisord.conf

ADD docker/swoole/www.ini /home/<USER>/www.ini

ADD docker/swoole/initial.sh /initial.sh

ADD ./ /home/<USER>/www

RUN chmod +x /initial.sh \
    && mkdir -p /home/<USER>
    && mkdir -p /home/<USER>/www/runtime \
    && chown -R www-data:www-data /home/<USER>/www/runtime

WORKDIR /home/<USER>/www/

EXPOSE 8080

EXPOSE 9090

CMD ["/initial.sh"]
