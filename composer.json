{"require": {"php": ">=8.2", "ext-json": "*", "ext-simplexml": "*", "ext-libxml": "*", "ext-bcmath": "*", "ext-redis": "*", "ext-calendar": "*", "ext-openssl": "*", "topthink/framework": "^8.1", "topthink/think-dumper": "^1.0", "topthink/think-ide-helper": "^2.0", "shuipf/tree": "^1.0", "shuipf/aliyun-log": "^1.0", "shuipf/pass": "^1.0", "shuipf/swoole": "dev-main", "shuipf/api": "^1.0", "shuipf/api-admin": "^1.0", "shuipf/export": "^1.0", "shuipf/api-build": "^1.0", "shuipf/queue": "^1.0", "w7corp/easywechat": "^6.7", "endroid/qr-code": "^4.6"}, "require-dev": {"shuipf/testing": "^1.0"}, "autoload": {"psr-4": {"app\\": "app", "tests\\": "tests"}, "psr-0": {"": "extend/"}}, "config": {"preferred-install": "dist", "allow-plugins": {}}, "scripts": {"post-autoload-dump": ["@php think service:discover", "@php think vendor:publish"]}, "repositories": {"shuipf/packages": {"type": "composer", "url": "https://packages.shuipf.com/"}}}