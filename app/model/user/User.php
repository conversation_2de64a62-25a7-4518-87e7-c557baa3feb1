<?php
// +----------------------------------------------------------------------
// | User 用户
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\model\user;

use common\model\BaseModel;
use common\traits\model\BaseDeleteTraits;
use common\traits\model\CacheInfoTraits;
use pass\Driver;
use pass\exception\UserDataException;
use pass\facade\Pass;
use pass\interfaces\User as InterfacesUser;
use Psr\SimpleCache\InvalidArgumentException;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\Exception;
use think\facade\Request;
use think\facade\Validate;
use think\helper\Str;
use think\Model;

/**
 * Class app\model\user\User
 * @property int $birthday 生日
 * @property int $current_login_time 本次登陆时间
 * @property int $gender 性别：0未知，1男，2女
 * @property int $id
 * @property int $last_login_time 上次登录时间
 * @property int $spread_uid 推广用户ID
 * @property int $status 状态，1正常，0锁定
 * @property string $avatar 头像
 * @property string $create_time 创建时间
 * @property string $current_login_ip 本次登陆ip
 * @property string $desc 简介
 * @property string $email 联系邮箱
 * @property string $last_login_ip 上次登录IP
 * @property string $mobile 移动号码
 * @property string $nickname 昵称
 * @property string $spread 渠道
 * @property string $update_time 修改时间
 * @property string $username 用户名
 */
class User extends BaseModel implements InterfacesUser
{
    use BaseDeleteTraits;
    use CacheInfoTraits;

    /**
     * 性别列表
     * @var string[]
     */
    public static $genderList = [
        0 => '未知',
        1 => '男',
        2 => '女',
    ];

    /**
     * 配置
     * @return array
     */
    protected function getOptions(): array
    {
        return [
            'type' => [
                'status' => 'integer',
                'gender' => 'integer',
                'last_login_time' => 'integer',
                'current_login_time' => 'integer',
                'create_time' => 'integer',
                'update_time' => 'integer',
            ],
        ];
    }

    /**
     * 新增前
     * @param static $object
     * @return void
     */
    protected static function onBeforeInsert($object)
    {
        $time = time();
        $object->status = 1;
        $object->last_login_time = $time;
        $object->current_login_time = $time;
    }

    /**
     * 修改前
     * @param static $object
     * @return void|bool
     */
    protected static function onBeforeUpdate($object)
    {
        $changedData = $object->getChangedData();
        // 昵称
        if (isset($changedData['nickname']) && $changedData['nickname']) {
            $isRepeat = self::where('nickname', $changedData['nickname'])
                ->where('id', '<>', $object->id)
                ->value('id');
            if ($isRepeat) {
                $object->error = '该昵称已被使用';
                return false;
            }
        }
        // 邮箱
        if (isset($changedData['email']) && $changedData['email']) {
            $isRepeat = self::where('email', $changedData['email'])
                ->where('id', '<>', $object->id)
                ->value('id');
            if ($isRepeat) {
                $object->error = '该邮箱已被使用';
                return false;
            }
        }
    }

    /**
     * 修改后
     * @param static $object
     * @return void
     * @throws InvalidArgumentException
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected static function onAfterUpdate($object)
    {
        // 刷新缓存
        self::info($object->id, false);
    }

    /**
     * 生日设置器
     * @param $value
     * @return int
     */
    protected function setBirthdayAttr($value)
    {
        // 年月日这种
        if (is_string($value)) {
            $value = strtotime($value);
        }
        return (int)$value;
    }

    /**
     * 性别设置器
     * @param $value
     * @return int
     */
    protected function setGenderAttr($value)
    {
        return match ($value) {
            '男', '1' => 1,
            '女', '2' => 2,
            default => 0,
        };
    }

    /**
     * 获取UID
     * @return int
     */
    public function uid(): int
    {
        if ($this->isEmpty()) {
            return 0;
        }
        return $this->id;
    }

    /**
     * 获取用户名
     * @return string
     */
    public function username(): string
    {
        if ($this->isEmpty()) {
            return '';
        }
        return $this->username;
    }

    /**
     * 编辑
     * @param array $data
     * @return bool
     */
    public function edit(array $data): bool
    {
        if (empty($data)) {
            $this->error = '数据不能为空';
            return false;
        }
        $validate = Validate::rule(
            [
                'birthday|生日' => ['dateFormat:Y-m-d'],
                'gender|性别' => ['number', 'in:0,1,2'],
            ]
        );
        if (!$validate->check($data)) {
            $this->error = $validate->getError();
            return false;
        }
        return $this->save($data);
    }

    /**
     * 根据明文密码获取密文
     * @param string $password
     * @return string
     */
    public function getHashPassword(string $password): string
    {
        return self::hashPassword($password, 'verify');
    }

    /**
     * 更新登录信息
     * @param string|null $ip
     * @return bool
     */
    public function currentLogin(string $ip = null): bool
    {
        // 更新登录信息
        $data = [
            'current_login_ip' => $ip ?: Request::ip(),
            'current_login_time' => time(),
            'last_login_time' => $this->current_login_time ?: 0,
            'last_login_ip' => $this->current_login_ip ?: '',
        ];
        return $this->save($data);
    }

    /**
     * 快速前台创建用户
     * @param array $data
     * @return User|Model
     * @throws Exception
     */
    public static function createByFront(array $data)
    {
        unset($data['id']);
        // 随机生成一个用户名
        $data['username'] ??= self::genderUsername();
        // 随机昵称
        $data['nickname'] ??= '乾元' . substr($data['mobile'], -4);
        // 随机头像
        $avatarList = [];
        $defaultKey = rand(0, 11);
        $data['avatar'] ??= $avatarList[$defaultKey] ?? '';
        $validate = Validate::rule(
            [
                'username|用户名' => ['require', 'min:6', 'max:12', 'unique:\\app\\model\\user\\User', 'alphaNum', function ($value) {
                    // 只能以字母开头
                    if (!preg_match('/^[A-Za-z]+$/', $value[0])) {
                        return '用户名只能以字母开头';
                    }
                    return true;
                }],
                'mobile|移动号码' => [function ($phone, $data) {
                    if (empty($phone)) {
                        return true;
                    }
                    // 检查手机号格式
                    if (!preg_match('/^1\d{10}$/', $phone)) {
                        return '手机号格式错误';
                    }
                    // 检查手机号是否存在
                    $value = self::where('mobile', $phone)
                        ->value('id');
                    if (!empty($value)) {
                        return '移动号码已存在';
                    }
                    return true;
                }],
            ]
        );
        if (!$validate->check($data)) {
            throw new Exception($validate->getError() ?: '参数错误');
        }
        return self::create($data);
    }

    /**
     * 获取用户信息
     * @param string|integer $identifier 用户名或者用户ID
     * @param string|null $password 密码,可为空
     * @param bool|string $auto =true 自动识别 $identifier
     * @return InterfacesUser
     * @throws UserDataException
     */
    public static function getUserInfo($identifier, string $password = null, bool $auto = true): InterfacesUser
    {
        if (empty($identifier)) {
            throw new UserDataException('用户不存在');
        }
        // 自动识别$identifier
        if (is_bool($auto)) {
            // 判断是uid还是用户名
            if (is_numeric($identifier)) {
                if ((int)$identifier > 99999999) {
                    $auto = 'mobile';
                } else {
                    $auto = 'uid';
                }
            } else {
                if (str_contains($identifier, '@')) {
                    $auto = 'email';
                } else {
                    $auto = 'username';
                }
            }
        }
        switch ($auto) {
            default:
            case 'username':
                $query = self::where('username', $identifier);
                break;
            case 'uid':
                $query = self::where('id', $identifier);
                break;
            case 'mobile':
                $query = self::where('mobile', $identifier);
                break;
            case 'email':
                $query = self::where('email', $identifier);
                break;
        }
        /**
         * @var InterfacesUser|static $userInfo
         */
        $userInfo = $query->findOrEmpty();
        if ($userInfo->isEmpty()) {
            throw new UserDataException('用户不存在');
        }
        // 密码验证
        if (!empty($password) && self::hashPassword($password, $userInfo['verify']) != $userInfo['password']) {
            throw new UserDataException('密码错误');
        }
        return $userInfo;
    }

    /**
     * 对明文密码，进行加密，返回加密后的密文密码
     * @param string $password 明文密码
     * @param string $verify 验证码
     * @return string
     */
    public static function hashPassword(string $password, string $verify = ''): string
    {
        return md5($password . md5($verify));
    }

    /**
     * 随机生成一个用户名
     * @return string
     */
    public static function genderUsername(): string
    {
        $username = Str::random(6, 3);
        if (self::checkUserNameIsRepeat($username)) {
            $username = self::genderUsername();
        }
        return $username;
    }

    /**
     * 判断用户名是否重复
     * @param string $username
     * @return bool
     */
    public static function checkUserNameIsRepeat(string $username): bool
    {
        $id = self::where('username', $username)->value('id');
        return $id > 0;
    }

    /**
     * 获取通行证对象
     * @param string $driver
     * @return Driver
     */
    public static function passService(string $driver = 'Qyapp'): Driver
    {
        return Pass::setDriver($driver);
    }
}
