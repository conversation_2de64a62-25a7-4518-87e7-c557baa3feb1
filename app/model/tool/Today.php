<?php
// +----------------------------------------------------------------------
// | 历史上今天
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\model\tool;

use app\model\BaseModel;
use common\traits\model\BaseDeleteTraits;
use common\traits\model\CacheInfoTraits;

/**
 * Class app\model\tool\Today
 * @property int $day 几号
 * @property int $id
 * @property int $month 月份
 * @property string $create_time 创建时间
 * @property string $date 日期
 * @property string $title 事件标题
 * @property string $update_time 修改时间
 */
class Today extends BaseModel
{
    use BaseDeleteTraits;
    use CacheInfoTraits;

    /**
     * 配置
     * @return array
     */
    protected function getOptions(): array
    {
        return [
            'name' => 'ToolToday',
            'type' => [
                'month' => 'integer',
                'day' => 'integer',
                'create_time' => 'integer',
                'update_time' => 'integer',
            ],
        ];
    }
}
