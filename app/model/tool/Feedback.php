<?php
// +----------------------------------------------------------------------
// | 意见反馈
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\model\tool;

use app\model\BaseModel;
use app\traits\model\BackgroundOperatorTraits;
use common\traits\model\BaseDeleteTraits;
use common\traits\model\CacheInfoTraits;
use think\helper\Str;

/**
 * Class app\model\tool\Feedback
 * @property array $pic 相关图片
 * @property array $video 相关视频
 * @property int $id
 * @property int $status 状态：0待处理，1已处理
 * @property int $uid 用户UID
 * @property string $content 反馈内容
 * @property string $create_time 创建时间
 * @property string $title 标题
 * @property string $update_time 更新时间
 * @property string $username 用户名
 * @property-read string $status_name
 */
class Feedback extends BaseModel
{
    use BaseDeleteTraits;
    use CacheInfoTraits;
    use BackgroundOperatorTraits;

    /**
     * 配置
     * @return array
     */
    protected function getOptions(): array
    {
        return [
            'name' => 'ToolFeedback',
            'type' => [
                'status' => 'integer',
                'uid' => 'integer',
                'pic' => 'array',
                'video' => 'array',
                'create_time' => 'integer',
                'update_time' => 'integer',
            ],
        ];
    }

    /**
     * 新增前
     * @param static $object
     * @return void|bool
     */
    protected static function onBeforeInsert($object)
    {
        if (empty($object->content)) {
            $object->error = '内容不能为空';
            return false;
        }
        if (empty($object->title)) {
            $str = str_replace(["\n", "\r", "\t"], '', $object->content);
            $object->title = Str::substr($str, 0, 30);
        }
    }

    /**
     * 状态名称获取器
     * @return string
     */
    protected function getStatusNameAttr()
    {
        return match ($this->status) {
            0 => '待处理',
            1 => '已处理',
            default => '未知'
        };
    }

    /**
     * 标记已处理
     * @return bool
     */
    public function process(): bool
    {
        $this->status = 1;
        return $this->save();
    }
}
