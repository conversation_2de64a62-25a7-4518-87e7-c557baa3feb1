<?php
// +----------------------------------------------------------------------
// | 版本升级
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\model\tool;

use app\model\BaseModel;
use app\traits\VersionConvertTraits;
use common\traits\model\BaseDeleteTraits;
use common\traits\model\CacheInfoTraits;
use think\facade\Validate;
use think\helper\Str;

/**
 * Class app\model\tool\Upgrade
 * @property int $id
 * @property int $min_version_int 最小升级版本号，数字版本号99.99.99
 * @property int $release_time 发布时间
 * @property int $status 状态：0待发布，1上线发布
 * @property int $update_force 是否强制更新：0非强制，1强制
 * @property int $update_method 更新方式：0静默更新，1提示更新
 * @property int $version_int 版本号，数字版本号99.99.99
 * @property string $create_time 创建时间
 * @property string $min_version 最小升级版本号，x.y.z
 * @property string $platform 平台：1安卓，2苹果，3鸿蒙，多个逗号间隔
 * @property string $title 更新标题
 * @property string $update_content 更新内容
 * @property string $update_time 更新时间
 * @property string $url 安装包地址
 * @property string $version 版本号，x.y.z
 * @property-read string $status_name
 * @property-read string $update_force_name
 * @property-read string $update_method_name
 */
class Upgrade extends BaseModel
{
    use BaseDeleteTraits;
    use CacheInfoTraits;
    use VersionConvertTraits;

    /**
     * 配置
     * @return array
     */
    protected function getOptions(): array
    {
        return [
            'name' => 'ToolUpgrade',
            'type' => [
                'status' => 'integer',
                'update_method' => 'integer',
                'update_force' => 'integer',
                'version_int' => 'integer',
                'min_version_int' => 'integer',
                'create_time' => 'integer',
                'update_time' => 'integer',
            ],
        ];
    }

    /**
     * 写入前
     * @param static $object
     * @return void|bool
     */
    protected static function onBeforeWrite($object)
    {
        // 版本号处理
        $version = $object->version;
        if (!empty($version)) {
            if (!self::isValidVersion($version)) {
                $object->error = '版本号不符合规则';
                return false;
            }
            $object->version_int = self::versionToNumber($version);
        }
        if (!empty($object->min_version)) {
            if (!self::isValidVersion($object->min_version)) {
                $object->error = '最小版本号不符合规则';
                return false;
            }
            $object->min_version = self::versionToNumber($object->min_version);
        }
        // 发布时间
        if ($object->status == 1) {
            $object->release_time = time();
        }
    }

    /**
     * 新增前
     * @param static $object
     * @return void|bool
     */
    protected static function onBeforeInsert($object)
    {
        if (empty($object->url)) {
            $object->error = '安装包地址不能为空';
            return false;
        }
        if (empty($object->title)) {
            $object->title = Str::substr(str_replace(["\n", "\r", " "], '', $object->update_content), 0, 30);
        }
    }

    /**
     * 状态名称获取器
     * @return string
     */
    protected function getStatusNameAttr()
    {
        return match ($this->status) {
            0 => '待发布',
            1 => '上线发布',
            default => '未知'
        };
    }

    /**
     * 更新方式名称获取器
     * @return string
     */
    protected function getUpdateMethodNameAttr()
    {
        return match ($this->status) {
            0 => '静默更新',
            1 => '提示更新',
            default => '未知'
        };
    }

    /**
     * 是否强制更新名称获取器
     * @return string
     */
    protected function getUpdateForceNameAttr()
    {
        return match ($this->status) {
            0 => '非强制',
            1 => '强制',
            default => '未知'
        };
    }

    /**
     * 添加
     * @param array $data
     * @return bool
     */
    public function plus(array $data): bool
    {
        if (empty($data)) {
            $this->error = '数据不能为空';
            return false;
        }
        $validate = Validate::rule(
            [
                'status|状态' => ['in:0,1'],
                'update_method|更新方式' => ['in:0,1'],
                'update_force|是否强制更新' => ['in:0,1'],
                'update_content|更新内容' => ['require'],
                'platform|平台' => [function ($value, $data) {
                    if (!isset($data['platform']) || self::isValidStringScope($value)) {
                        return true;
                    }
                    return '平台不在1,2,3范围内';
                }],
                'version|版本号' => ['require', function ($value, $data) {
                    if (!isset($data['version']) || self::isValidVersion($value)) {
                        return true;
                    }
                    return "版本号不符合x.y.z格式:{$value}";
                }],
                'min_version|最小升级版本号' => [function ($value, $data) {
                    if (empty($data['min_version']) || self::isValidVersion($value)) {
                        return true;
                    }
                    return "最小升级版本号不符合x.y.z格式:{$value}";
                }],
                'url|安装包地址' => ['url', function ($value, $data) {
                    if (empty($value) && Str::contains($data['platform'], '1')) {
                        return '安装包地址不能为空';
                    }
                    return true;
                }],
            ]
        );
        if (!$validate->check($data)) {
            $this->error = $validate->getError() ?: '参数验证错误';
            return false;
        }
        return $this->save($data);
    }

    /**
     * 编辑
     * @param array $data
     * @return bool
     */
    public function edit(array $data): bool
    {
        if (empty($data)) {
            $this->error = '数据不能为空';
            return false;
        }
        $validate = Validate::rule(
            [
                'status|状态' => ['in:0,1'],
                'update_method|更新方式' => ['in:0,1'],
                'update_force|是否强制更新' => ['in:0,1'],
                'platform|平台' => [function ($value, $data) {
                    if (!isset($data['platform']) || self::isValidStringScope($value)) {
                        return true;
                    }
                    return '平台不在1,2,3范围内';
                }],
                'version|版本号' => [function ($value, $data) {
                    if (!isset($data['version']) || self::isValidVersion($value)) {
                        return true;
                    }
                    return "版本号不符合x.y.z格式:{$value}";
                }],
                'min_version|最小升级版本' => [function ($value, $data) {
                    if (empty($data['min_version']) || self::isValidVersion($value)) {
                        return true;
                    }
                    return "最小升级版本号不符合x.y.z格式:{$value}";
                }],
                'url|安装包地址' => ['url'],
            ]
        );
        if (!$validate->check($data)) {
            $this->error = $validate->getError() ?: '参数验证错误';
            return false;
        }
        return $this->save($data);
    }

    /**
     * 根据逗号间隔，判断是否符合要求
     * @param string $str
     * @param array $allowedNumbers 定义允许的数字
     * @return bool
     */
    public static function isValidStringScope(string $str, array $allowedNumbers = ['1', '2', '3']): bool
    {
        // 将字符串按逗号分隔成数组
        $numbers = explode(',', $str);
        // 遍历数组，检查每个元素是否符合要求
        foreach ($numbers as $number) {
            if (!in_array($number, $allowedNumbers)) {
                return false;
            }
        }
        return true;
    }
}
