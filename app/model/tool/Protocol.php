<?php
// +----------------------------------------------------------------------
// | 协议内容
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\model\tool;

use app\model\BaseModel;
use common\traits\model\BaseDeleteTraits;
use common\traits\model\CacheInfoTraits;
use think\facade\Validate;
use think\helper\Str;

/**
 * Class app\model\tool\Protocol
 * @property int $id
 * @property int $implement_time 生效时间
 * @property string $at 标识
 * @property string $content 内容
 * @property string $create_time 创建时间
 * @property string $title 协议名称
 * @property string $update_time 修改时间
 * @property string $version_str 版本
 */
class Protocol extends BaseModel
{
    use BaseDeleteTraits;
    use CacheInfoTraits;

    /**
     * 配置
     * @return array
     */
    protected function getOptions(): array
    {
        return [
            'name' => 'ToolProtocol',
            'type' => [
                'implement_time' => 'integer',
                'create_time' => 'integer',
                'update_time' => 'integer',
            ],
        ];
    }

    /**
     * 写入前
     * @param static $object
     * @return void
     */
    protected static function onBeforeWrite($object)
    {
        // 标识处理
        $object->at = Str::lower($object->at);
    }

    /**
     * 新增前
     * @param static $object
     * @return void|bool
     */
    protected static function onBeforeInsert($object)
    {
        if (empty($object->at)) {
            $object->error = '协议标识不能为空';
            return false;
        }
    }

    /**
     * 生效时间修改器
     * @param int|string $value
     * @return int
     */
    protected function setImplementTimeAttr($value)
    {
        if (!is_numeric($value)) {
            $value = strtotime($value);
        }
        return (int)$value;
    }

    /**
     * 添加
     * @param array $data
     * @return bool
     */
    public function plus(array $data): bool
    {
        if (empty($data)) {
            $this->error = '数据不能为空';
            return false;
        }
        $validate = Validate::rule(
            [
                'title|协议名称' => ['require', 'unique:tool_protocol'],
                'at|协议标识' => ['require', 'unique:tool_protocol'],
                'version_str|协议版本' => ['require'],
                'content|协议内容' => ['require'],
                'implement_time|生效时间' => ['require', 'dateFormat:Y-m-d H:i:s'],
            ]
        );
        if (!$validate->check($data)) {
            $this->error = $validate->getError() ?: '参数验证错误';
            return false;
        }
        return $this->save($data);
    }

    /**
     * 编辑
     * @param array $data
     * @return bool
     */
    public function edit(array $data): bool
    {
        if (empty($data)) {
            $this->error = '数据不能为空';
            return false;
        }
        $validate = Validate::rule(
            [
                'title|协议名称' => ['require', "unique:tool_protocol,title,{$this->id}"],
                'at|协议标识' => ['require', "unique:tool_protocol,at,{$this->id}"],
                'implement_time|生效时间' => ['dateFormat:Y-m-d H:i:s'],
            ]
        );
        if (!$validate->check($data)) {
            $this->error = $validate->getError() ?: '参数验证错误';
            return false;
        }
        return $this->save($data);
    }
}
