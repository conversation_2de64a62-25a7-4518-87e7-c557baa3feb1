<?php
// +----------------------------------------------------------------------
// | 解梦
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\model\tool;

use app\model\BaseModel;
use app\traits\model\BackgroundOperatorTraits;
use common\traits\model\BaseDeleteTraits;
use common\traits\model\CacheInfoTraits;
use think\facade\Validate;
use think\model\concern\SoftDelete;

/**
 * Class app\model\tool\Jm
 * @property int $category 分类
 * @property int $delete_time 删除时间
 * @property int $hits 点击数
 * @property int $id
 * @property int $isthumb 是否有图片
 * @property int $release_time 发布时间
 * @property int $status 状态 0 禁用 1 启用
 * @property string $author 作者
 * @property string $content 内容
 * @property string $create_time 创建时间
 * @property string $description 简短描述
 * @property string $keyword 关键字
 * @property string $source 来源
 * @property string $thumb 图片
 * @property string $title 标题
 * @property string $update_time 更新时间
 * @method static \think\db\Query onlyTrashed()
 * @method static \think\db\Query withTrashed()
 */
class Jm extends BaseModel
{
    use BaseDeleteTraits;
    use CacheInfoTraits;
    use SoftDelete;
    use BackgroundOperatorTraits;

    /**
     * 分类
     * @var array|string[]
     */
    public static array $categoryList = [
        1 => '梦见人物',
        2 => '梦见动物',
        3 => '梦见植物',
        4 => '梦见物品',
        5 => '梦见身体',
        6 => '梦见鬼神',
        7 => '梦见建筑',
        8 => '梦见自然',
        9 => '梦见生活',
        11 => '梦见情爱',
        12 => '梦见分离',
        13 => '梦见活动',
        14 => '梦见孕妇',
        15 => '梦见其它',
    ];

    /**
     * 配置
     * @return array
     */
    protected function getOptions(): array
    {
        return [
            'name' => 'ToolJm',
            'type' => [
                'category' => 'integer',
                'status' => 'integer',
                'isthumb' => 'integer',
                'hits' => 'integer',
                'release_time' => 'integer',
                'create_time' => 'integer',
                'update_time' => 'integer',
            ],
            'defaultSoftDelete' => 0,
        ];
    }

    /**
     * 添加
     * @param array $data
     * @return bool
     */
    public function plus(array $data): bool
    {
        if (empty($data)) {
            $this->error = '数据不能为空';
            return false;
        }
        $validate = Validate::rule(
            [
                'category|分类' => ['require', 'number', 'gt:0', function ($value) {
                    if (!isset(self::$categoryList[$value])) {
                        return '分类不存在';
                    }
                    return true;
                }],
                'title|标题' => ['require', 'unique:ToolJm'],
                'content|正文' => ['require'],
            ]
        );
        if (!$validate->check($data)) {
            $this->error = $validate->getError() ?: '验证失败';
            return false;
        }
        self::startTrans();
        try {
            if ($this->save($data)) {
                self::commit();
                return true;
            }
        } catch (\Exception | \Throwable $e) {
            $this->error = $e->getMessage();
        }
        self::rollback();
        return false;
    }

    /**
     * 编辑
     * @param array $data
     * @return bool
     */
    public function edit(array $data): bool
    {
        if (empty($data)) {
            $this->error = '数据不能为空';
            return false;
        }
        $validate = Validate::rule(
            [
                'category|分类' => ['number', 'gt:0', function ($value, $data) {
                    if (!isset($data['category'])) {
                        return true;
                    }
                    if (!isset(self::$categoryList[$value])) {
                        return '分类不存在';
                    }
                    return true;
                }],
                'title|标题' => ["unique:ToolJm,title,{$this->id}"],
            ]
        );
        if (!$validate->check($data)) {
            $this->error = $validate->getError() ?: '验证失败';
            return false;
        }
        self::startTrans();
        try {
            if ($this->save($data)) {
                self::commit();
                return true;
            }
        } catch (\Exception | \Throwable $e) {
            $this->error = $e->getMessage();
        }
        self::rollback();
        return false;
    }
}
