<?php
// +----------------------------------------------------------------------
// | 测算-订单
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\model\cesuan;

use app\model\BaseModel;
use common\traits\model\BaseDeleteTraits;
use common\traits\model\CacheInfoTraits;
use think\Exception;
use think\model\concern\SoftDelete;

/**
 * Class app\model\cesuan\Order
 * @property array $info 扩展信息
 * @property float $money 订单金额
 * @property float $pay_money 支付金额
 * @property int $birthday 用户资料1：生日
 * @property int $birthday2 用户资料2：生日
 * @property int $delete_time 删除时间
 * @property int $gender 用户资料1：性别，0未填写，1是男，2是女
 * @property int $gender2 用户资料2：性别，0未填写，1是男，2是女
 * @property int $id 主键id
 * @property int $order_id 订单ID
 * @property int $pay_status 支付状态，0未支付，1已支付
 * @property int $pay_time 支付时间
 * @property int $servers_id 应用ID
 * @property int $status 订单状态，0未完成，1已经完成，-1已关闭
 * @property int $theme_id 主题ID
 * @property int $uid 会员UID
 * @property string $create_time 创建时间
 * @property string $order_sn 订单编号
 * @property string $order_title 订单标题
 * @property string $order_url 订单地址
 * @property string $pay_order_sn 支付订单号
 * @property string $servers_ico 应用图标
 * @property string $servers_name 应用名称
 * @property string $spread 推广渠道
 * @property string $spread_name 推广渠道名称
 * @property string $update_time 最后修改时间
 * @property string $username 用户资料1：用户姓名
 * @property string $username2 用户资料2：用户姓名
 * @property-read string $server_url
 * @method static \think\db\Query onlyTrashed()
 * @method static \think\db\Query withTrashed()
 */
class Order extends BaseModel
{
    use SoftDelete;
    use BaseDeleteTraits;
    use CacheInfoTraits;

    /**
     * 订单状态
     * @var array
     */
    public static array $statusList = [
        0 => '未完成',
        1 => '已完成',
        -1 => '已关闭',
    ];

    /**
     * 支付状态列表
     * @var array
     */
    public static array $payStatusList = [
        0 => '未支付',
        1 => '已支付',
        2 => '手动确认支付',
        -1 => '已退款',
        -2 => '已手动退款',
    ];

    /**
     * 性别状态列表
     * @var array
     */
    public static array $genderList = [
        0 => '未填写',
        1 => '男',
        2 => '女',
    ];

    /**
     * 配置
     * @return array
     */
    protected function getOptions(): array
    {
        return [
            'name' => 'CesuanOrder',
            'type' => [
                'uid' => 'integer',
                'order_id' => 'integer',
                'status' => 'integer',
                'servers_id' => 'integer',
                'theme_id' => 'integer',
                'money' => 'float:2',
                'pay_money' => 'float:2',
                'pay_status' => 'integer',
                'pay_time' => 'integer',
                'gender' => 'integer',
                'birthday' => 'integer',
                'gender2' => 'integer',
                'birthday2' => 'integer',
                'info' => 'array',
                'delete_time' => 'integer',
                'update_time' => 'integer',
                'create_time' => 'integer',
            ],
            'defaultSoftDelete' => 0,
        ];
    }

    /**
     * 根据订单地址得到应用首页
     * @param $value
     * @param $data
     * @return string
     */
    protected function getServerUrlAttr($value, $data)
    {
        return preg_replace('~/([^/]+)/([^/]+)/orders\.html.*~', '/$1/$2.html', $this->order_url);
    }

    /**
     * 绑定用户
     * @param int $uid
     * @return bool
     */
    public function bindUid(int $uid): bool
    {
        $this->uid = $uid;
        return $this->save();
    }

    /**
     * 同步
     * @param array $order
     * @return static
     * @throws Exception
     */
    public static function sync(array $order): static
    {
        if (empty($order['order_sn'])) {
            throw new Exception('订单编号不能为空');
        }
        $info = self::where('order_sn', $order['order_sn'])->findOrEmpty();
        $data = [
            'uid' => $order['uid'] ?? null,
            'order_sn' => $order['order_sn'] ?? null,
            'order_url' => $order['order_url'] ?? null,
            'order_id' => $order['id'] ?? null,
            'status' => $order['status'] ?? null,
            'servers_id' => $order['servers_id'] ?? null,
            'servers_name' => $order['servers_name'] ?? null,
            'servers_ico' => $order['servers_ico'] ?? null,
            'order_title' => $order['order_title'] ?? null,
            'spread' => $order['spread'] ?? null,
            'spread_name' => $order['spread_name'] ?? null,
            'theme_id' => $order['theme_id'] ?? null,
            'money' => $order['money'] ?? null,
            'pay_money' => $order['pay_money'] ?? null,
            'pay_status' => $order['pay_status'] ?? null,
            'pay_time' => $order['pay_time'] ?? null,
            'pay_order_sn' => $order['pay_order_sn'] ?? null,
            'username' => $order['username'] ?? null,
            'gender' => $order['gender'] ?? null,
            'birthday' => $order['birthday'] ?? null,
            'username2' => $order['username2'] ?? null,
            'gender2' => $order['gender2'] ?? null,
            'birthday2' => $order['birthday2'] ?? null,
            'info' => $order['info'] ?? null,
        ];
        // 过滤掉值为 null 的元素
        $data = array_filter($data, function ($value) {
            return $value !== null;
        });
        if (!$info->save($data)) {
            throw new Exception($info->getError() ?: '更新失败');
        }
        return $info;
    }
}
