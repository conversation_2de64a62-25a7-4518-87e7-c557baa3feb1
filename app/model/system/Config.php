<?php
// +----------------------------------------------------------------------
// | 配置表
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\model\system;

use app\model\BaseModel;
use app\traits\model\system\config\SetConfigurationTraits;
use ArrayAccess;
use common\traits\model\BaseDeleteTraits;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\helper\Arr;

/**
 * Class app\model\system\Config
 * @property int $id
 * @property string $create_time 创建时间
 * @property string $iden 配置标识
 * @property string $item 项目名称
 * @property string $tips 提示
 * @property string $update_time 修改时间
 * @property string $value 配置内容
 */
class Config extends BaseModel
{
    use BaseDeleteTraits;
    use SetConfigurationTraits;

    /**
     * 配置
     * @return array
     */
    protected function getOptions(): array
    {
        return [
            'type' => [
                'create_time' => 'integer',
                'update_time' => 'integer',
            ],
        ];
    }

    /**
     * 写入后
     * @param static $object
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected static function onAfterWrite($object)
    {
        // 更新缓存
        self::info($object->iden, false);
    }

    /**
     * 获取配置
     * @param string $iden 配置标识
     * @param string|null $field 指定字段，null返回全部
     * @param null $default 不存在时的返回值
     * @return array|ArrayAccess|mixed
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public static function conf(string $iden, string $field = null, $default = null)
    {
        $info = self::info($iden);
        if (empty($info) || $info->isEmpty()) {
            return $default;
        }
        $config = $info->config();
        if (is_array($config)) {
            return Arr::get($config, $field, $default);
        }
        return $config;
    }

    /**
     * 获取单独配置
     * @param string $iden 配置标识
     * @param bool $isCache 是否缓存
     * @return Config
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public static function info(string $iden, bool $isCache = true)
    {
        $cacheKeyName = "config/{$iden}";
        if (!$isCache) {
            self::master()
                ->getConnection()
                ->getCache()
                ->delete($cacheKeyName);
        }
        return self::where('iden', $iden)
            ->cache($cacheKeyName, 3600)
            ->failException()
            ->find();
    }
}
