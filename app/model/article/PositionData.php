<?php
// +----------------------------------------------------------------------
// | PositionData 推荐位数据
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\model\article;

use app\interfaces\PositionDataInterface;
use app\model\BaseModel;
use common\traits\model\BaseDeleteTraits;
use Psr\SimpleCache\InvalidArgumentException;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\Exception;
use think\facade\Validate;
use think\model\contract\Modelable;
use think\model\relation\HasOne;
use Throwable;


/**
 * Class app\model\article\PositionData
 * @property array $data 数据
 * @property int $id ID
 * @property int $is_thumb 是否存在缩略图
 * @property int $listorder 排序
 * @property int $original_catid 原始分类ID
 * @property int $original_id 原始主键ID
 * @property int $posid 推荐位ID
 * @property int $synedit 是否同步编辑，1是
 * @property int $type 类型：1 文章
 * @property string $create_time 创建时间
 * @property string $thumb 缩略图
 * @property string $title 标题
 * @property string $update_time 更新时间
 * @property-read \app\model\article\Position $position
 */
class PositionData extends BaseModel
{
    use BaseDeleteTraits;

    /**
     * 配置
     * @return array
     */
    protected function getOptions(): array
    {
        return [
            'type' => [
                'type' => 'integer',
                'posid' => 'integer',
                'original_id' => 'integer',
                'original_catid' => 'integer',
                'data' => 'json',
                'synedit' => 'integer',
                'listorder' => 'integer',
            ],
            'append' => [],
            'defaultSoftDelete' => 0,
        ];
    }

    /**
     * 一对一关联 推荐位
     * @return HasOne
     */
    public function position()
    {
        return $this->hasOne('Position', 'id', 'posid');
    }

    /**
     * 新增前
     * @param static $object
     */
    protected static function onBeforeInsert($object)
    {
        $changedData = $object->getChangedData();
        // 是否有缩略图
        if (isset($changedData['thumb'])) {
            $object->is_thumb = $object->thumb ? 1 : 0;
        }
    }

    /**
     * 新增后
     * @param static $object
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected static function onAfterInsert($object)
    {
        $object->maxNum();
    }

    /**
     * 更新前
     * @param static $object
     */
    protected static function onBeforeUpdate($object)
    {
        $changedData = $object->getChangedData();
        // 是否有缩略图
        if (isset($changedData['thumb'])) {
            $object->is_thumb = $object->thumb ? 1 : 0;
        }
    }

    /**
     * 删除后
     * @param static $object
     * @throws DataNotFoundException
     * @throws DbException
     * @throws InvalidArgumentException
     * @throws ModelNotFoundException
     */
    protected static function onAfterDelete($object)
    {
        $object->callbackSavePositionStatus();
    }

    /**
     * 数据修复重建
     * @return bool
     * @throws DataNotFoundException
     * @throws DbException
     * @throws InvalidArgumentException
     * @throws ModelNotFoundException
     */
    public function again(): bool
    {
        switch ($this->type) {
            // 文章
            case 1:
                $object = Article::find($this->original_id);
                break;
            default:
                return true;
        }
        if (empty($object)) {
            return false;
        }
        // 通过 推荐位数据接口对象 更新 推荐位数据
        return $this->upData($object);
    }

    /**
     * 回调 对应信息推荐位状态 更新
     * @return bool
     * @throws DataNotFoundException
     * @throws DbException
     * @throws InvalidArgumentException
     * @throws ModelNotFoundException
     */
    public function callbackSavePositionStatus(): bool
    {
        $is = self::where('type', $this->type)
            ->where('original_id', $this->original_id)
            ->where('original_catid', $this->original_catid)
            ->value('id');
        if (!$is) {
            switch ($this->type) {
                case 1:
                    Article::where(['id' => $this->original_id])->update(['posid' => 0]);
                    Article::info($this->original_id, false);
                    break;
            }
        }
        return true;
    }

    /**
     * 编辑
     * @param array $data
     * @return bool
     */
    public function edit(array $data): bool
    {
        if (empty($data)) {
            $this->error = '数据不能为空';
            return false;
        }
        // 暂时不支持data修改
        unset($data['data']);
        $validate = Validate::rule(
            [
                'title|标题' => ["unique:Position,name,{$this->id}"],
                'thumb|缩略图' => ['url'],
                'synedit|是否同步编辑' => ['in:0,1'],
                'listorder|排序' => ['number'],
            ]
        );
        if (!$validate->check($data)) {
            $this->error = $validate->getError() ?: '验证失败';
            return false;
        }
        return $this->save($data);
    }

    /**
     * 通过 推荐位数据接口对象 更新 推荐位数据
     * @param PositionDataInterface $object 数据
     * @return bool
     * @throws DataNotFoundException
     * @throws DbException
     * @throws InvalidArgumentException
     * @throws ModelNotFoundException
     */
    public function upData(PositionDataInterface $object): bool
    {
        if (!$this->synedit) {
            return true;
        }
        // 组合成推荐位所需要的数据
        $position = Position::info($this->posid);
        if (empty($position)) {
            $this->error = '推荐位不存在';
            return false;
        }
        $positionData = $object->generatePositionData();
        $data = $position->generateData($object);
        $saveData = [
            // 基本数据
            'original_id' => $positionData['original_id'],
            'original_catid' => $positionData['original_catid'],
            'type' => $positionData['type'],
            'title' => $positionData['title'],
            'thumb' => $positionData['thumb'],
            // 扩展数据，根据推荐位需要
            'data' => $data,
        ];
        return $this->save($saveData);
    }

    /**
     * 更新推荐位最大信息存储量
     * @return bool
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function maxNum(): bool
    {
        // 最大存储量
        $maxNum = $this->position->max_num;
        if ($maxNum < 1) {
            return true;
        }
        $maxList = self::where(['posid' => $this->position->id])
            ->order(['listorder' => 'desc', 'id' => 'desc'])
            ->limit($maxNum, 10)
            ->select();
        foreach ($maxList as $rs) {
            $rs->rm();
            $rs->callbackSavePositionStatus();
        }
        return true;
    }

    /**
     * 批量删除推荐位里的信息
     * @param array $ids
     * @return bool
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * @throws Throwable
     */
    public static function rmAll(array $ids): bool
    {
        if (empty($ids)) {
            return true;
        }
        /**
         * @var static[] $list
         */
        $list = self::whereIn('id', $ids)->select();
        foreach ($list as $rs) {
            $rs->rm();
            $rs->callbackSavePositionStatus();
        }
        return true;
    }

    /**
     * 增加推荐位数据
     * @param int $posid 推荐位ID
     * @param PositionDataInterface $object 数据
     * @return Modelable
     * @throws DataNotFoundException
     * @throws DbException
     * @throws Exception
     * @throws ModelNotFoundException
     * @throws InvalidArgumentException
     */
    public static function add(int $posid, PositionDataInterface $object)
    {
        // 组合成推荐位所需要的数据
        $position = Position::info($posid);
        if (empty($position)) {
            throw new Exception('推荐位不存在');
        }
        $positionData = $object->generatePositionData();
        $data = $position->generateData($object);
        $saveData = [
            'posid' => $posid,
            // 是否同步编辑，默认是
            'synedit' => $positionData['synedit'] ?? 1,
            // 基本数据
            'original_id' => $positionData['original_id'],
            'original_catid' => $positionData['original_catid'],
            'type' => $positionData['type'],
            'title' => $positionData['title'],
            'thumb' => $positionData['thumb'],
            // 扩展数据，根据推荐位需要
            'data' => $data,
        ];
        return self::create($saveData);
    }
}
