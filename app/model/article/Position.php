<?php
// +----------------------------------------------------------------------
// | Position 推荐位
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\model\article;

use app\interfaces\PositionDataInterface;
use app\model\BaseModel;
use common\traits\model\BaseDeleteTraits;
use common\traits\model\CacheInfoTraits;
use Psr\SimpleCache\InvalidArgumentException;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\Exception;
use think\facade\Validate;
use think\model\relation\HasMany;
use Throwable;

/**
 * Class app\model\article\Position
 * @property int $id 推荐位id
 * @property int $listorder 排序
 * @property int $max_num 最大存储数据量
 * @property int $platform 平台 0 通用 1 PC 2 手机 3 App
 * @property string $catid 分类id
 * @property string $name 名称
 * @property-read \app\model\article\PositionData[] $position_data
 * @property-read string $platform_name
 */
class Position extends BaseModel
{
    use BaseDeleteTraits;
    use CacheInfoTraits;

    /**
     * 推荐位终端
     * @var array
     */
    public static array $platformArr = [
        1 => '电脑',
        2 => '手机',
        3 => 'App',
    ];

    /**
     * 配置
     * @return array
     */
    protected function getOptions(): array
    {
        return [
            'type' => [
                'platform' => 'integer',
                'max_num' => 'integer',
                'listorder' => 'integer',
            ],
            'defaultSoftDelete' => 0,
        ];
    }

    /**
     * 一对多关联 推荐位数据
     * @return HasMany
     */
    public function positionData()
    {
        return $this->hasMany(PositionData::class, 'posid', 'id');
    }

    /**
     * 删除后
     * @param static $object
     */
    protected static function onAfterDelete($object)
    {
        // 推荐位删除
        foreach ($object->position_data as $each) {
            /**
             * @var $each PositionData
             */
            $each->delete();
        }
    }

    /**
     * catid 修改器
     * @param $catid
     * @return string
     */
    protected function setCatidAttr($catid): string
    {
        if (empty($catid)) {
            return '';
        }
        if (is_array($catid)) {
            $catid = array_filter(array_unique($catid));
            $catid = implode(',', $catid);
        }
        return $catid;
    }

    /**
     * 平台名称获取器
     * @return string
     */
    protected function getPlatformNameAttr(): string
    {
        return self::$platformArr[$this->platform] ?? '未知';
    }

    /**
     * 生成推荐位需要的扩展数据 data
     * @param PositionDataInterface $object 数据
     * @return array
     */
    public function generateData(PositionDataInterface $object): array
    {
        $positionData = $object->generatePositionData();
        switch ($positionData['type']) {
            // 文章
            case 1:
                $posData = [
                    'title' => $positionData['title'],
                    'short_title' => $positionData['short_title'],
                    'tags' => $positionData['tags'],
                    'keywords' => $positionData['keywords'],
                    'author' => $positionData['author'],
                    'source' => $positionData['source'],
                    'description' => $positionData['description'],
                    'release_time' => $positionData['release_time'],
                ];
                break;
            default:
                $posData = [];
                break;
        }
        return $posData;
    }

    /**
     * 添加
     * @param array $data 数据
     * @return bool
     */
    public function plus(array $data): bool
    {
        if (empty($data)) {
            $this->error = '数据不能为空';
            return false;
        }
        $validate = Validate::rule(
            [
                'name|推荐位名称' => ['require', 'unique:Position,name'],
                'platform|平台id' => ['require', 'in:0,1,2,3'],
                'max_num|最大存储数据量' => ['require', 'number'],
            ]
        );
        if (!$validate->check($data)) {
            $this->error = $validate->getError() ?: '验证失败';
            return false;
        }
        return $this->save($data);
    }

    /**
     * 修改
     * @param array $data 数据
     * @return bool
     */
    public function edit(array $data): bool
    {
        if (empty($data)) {
            $this->error = '数据不能为空';
            return false;
        }
        $validate = Validate::rule(
            [
                'name|推荐位名称' => ["unique:Position,name,{$this->id}"],
                'platform|平台id' => ['in:0,1,2,3'],
                'max_num|最大存储数据量' => ['number'],
            ]
        );
        if (!$validate->check($data)) {
            $this->error = $validate->getError() ?: '验证失败';
            return false;
        }
        return $this->save($data);
    }

    /**
     * 保存数据到推荐位
     * @param int|array $posid 推荐位ID
     * @param PositionDataInterface $object 数据
     * @param bool $isDel 是否删除不存在的推荐位
     * @return bool
     * @throws DataNotFoundException
     * @throws DbException
     * @throws Exception
     * @throws InvalidArgumentException
     * @throws ModelNotFoundException
     * @throws Throwable
     */
    public static function saveData(int | array $posid, PositionDataInterface $object, bool $isDel = true): bool
    {
        if (empty($posid)) {
            return false;
        }
        $data = $object->generatePositionData();
        $posid = is_array($posid) ? $posid : [$posid];
        // 以推荐位ID为下标的数组
        $pid = [];
        foreach ($posid as $i) {
            if (empty($i)) {
                continue;
            }
            $pid[$i] = $i;
        }
        /**
         * @var PositionData[] $oldList
         */
        $oldList = PositionData::where('original_id', $data['original_id'])
            ->where('original_catid', $data['original_catid'])
            ->limit(200)
            ->select();
        foreach ($oldList as $rs) {
            // 是否已经去除的
            if ($isDel && !in_array($rs['posid'], $pid)) {
                $rs->rm();
                continue;
            }
            $rs->upData($object);
            unset($pid[$rs['posid']]);
        }
        // 新增
        if (!empty($pid)) {
            foreach ($pid as $id) {
                PositionData::add($id, $object);
            }
        }
        return true;
    }
}
