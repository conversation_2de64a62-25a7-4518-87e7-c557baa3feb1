<?php
// +----------------------------------------------------------------------
// | Tags 标签
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\model\article;

use app\model\BaseModel;
use Psr\SimpleCache\InvalidArgumentException;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\model\relation\HasMany;

/**
 * Class app\model\article\Tags
 * @property int $display 是否显示 1为显示
 * @property int $id
 * @property int $views 访问数
 * @property string $create_time 创建时间
 * @property string $name 名称
 * @property string $update_time 修改时间
 * @property-read \app\model\article\TagsList[] $tags_list
 */
class Tags extends BaseModel
{
    /**
     * 配置
     * @return array
     */
    protected function getOptions(): array
    {
        return [
            'type' => [
                'views' => 'integer',
                'update_time' => 'integer',
                'create_time' => 'integer',
            ],
            'defaultSoftDelete' => 0,
        ];
    }

    /**
     * 一对多关联 标签数据
     * @return HasMany
     */
    public function tagsList()
    {
        return $this->hasMany(TagsList::class, 'name', 'name');
    }

    /**
     * 写入后
     * @param static $object
     * @throws DataNotFoundException
     * @throws DbException
     * @throws InvalidArgumentException
     * @throws ModelNotFoundException
     */
    protected static function onAfterWrite($object)
    {
        self::info($object->getData('name'));
    }

    /**
     * 删除前
     * @param static $object
     * @return false|void
     */
    protected static function onBeforeDelete($object)
    {
        $is = TagsList::where('name', $object->name)->value('id');
        if ($is) {
            $object->error = '该标签存在数据';
            return false;
        }
    }

    /**
     * 获取内容详情
     * @param string $name 标签
     * @param bool $newCache true 强制刷新缓存
     * @return Tags|array|mixed
     * @throws InvalidArgumentException
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public static function info(string $name, bool $newCache = false)
    {
        $cacheKeyName = "tags/info/{$name}";
        // 清理缓存
        if ($newCache) {
            self::master()
                ->getConnection()
                ->getCache()
                ->delete($cacheKeyName);
        }
        $data = self::cacheAlways($cacheKeyName, 3600)
            ->where('name', $name)
            ->find();
        return $data;
    }

    /**
     * 标签数据更新
     * @param array $data
     * @return bool
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public static function tagsUpdates(array $data): bool
    {
        if (empty($data)) {
            return true;
        }
        foreach ($data as $name) {
            $name = trim($name);
            $info = self::where('name', $name)->find();
            if (empty($info)) {
                $info = new static();
                $info->save(['name' => $name]);
            }
        }
        return true;
    }

    /**
     * 隐藏或者显示
     * @param int|null $display
     * @return bool
     */
    public function display(int $display = null): bool
    {
        if (is_null($display)) {
            $this->display = $this->display ? 0 : 1;
        } else {
            $this->display = $display ? 1 : 0;
        }
        return $this->save();
    }
}
