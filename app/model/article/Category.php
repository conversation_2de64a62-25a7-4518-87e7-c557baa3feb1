<?php
// +----------------------------------------------------------------------
// | Category 文章分类
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\model\article;

use app\model\BaseModel;
use common\traits\model\BaseDeleteTraits;
use Psr\SimpleCache\InvalidArgumentException;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\Exception;
use think\facade\Queue;
use think\facade\Validate;
use think\helper\Arr;
use think\Model;

/**
 * Class app\model\article\Category
 * @property int $article_count 已审核文章统计数，包含所有子孙节点的文章
 * @property int $child 是否存在子栏目，1存在
 * @property int $delete_time 删除时间
 * @property int $id 分类id
 * @property int $listorder 排序
 * @property int $parentid 父id
 * @property int $status 状态 1启用 0禁用
 * @property string $arrchildid 所有子栏目ID
 * @property string $arrparentid 所有父栏目ID
 * @property string $create_time 创建时间
 * @property string $name 分类名
 * @property string $seo_description 栏目描述
 * @property string $seo_keywords 关键词
 * @property string $update_time 更新时间
 * @property-read array $arr_parent_name
 * @property-read string $status_name
 */
class Category extends BaseModel
{
    use BaseDeleteTraits;

    /**
     * 状态列表
     * @var array
     */
    public static array $statustxt = [
        1 => '启用',
        0 => '禁用',
    ];

    /**
     * 配置
     * @return array
     */
    protected function getOptions(): array
    {
        return [
            'type' => [
                'child' => 'integer',
                'parentid' => 'integer',
                'type' => 'integer',
                'status' => 'integer',
                'listorder' => 'integer',
                'setting' => 'array',
                'create_time' => 'integer',
                'update_time' => 'integer',
            ],
            'defaultSoftDelete' => 0,
        ];
    }

    /**
     * 新增后
     * @param static $object
     * @return void
     */
    protected static function onAfterInsert($object)
    {
        // 投递异步任务
        self::deliverTask($object);
    }

    /**
     * 写入后
     * @param static $object
     * @throws DataNotFoundException
     * @throws DbException
     * @throws InvalidArgumentException
     * @throws ModelNotFoundException
     */
    protected static function onAfterWrite($object)
    {
        $changedData = $object->getChangedData();
        // 更新缓存
        self::info($object->id, '', true);
        self::getAllName(false);

        // 投递异步任务
        if (isset($changedData['parentid'])) {
            self::deliverTask($object);
        }
    }

    /**
     * 删除前
     * @param static $object
     */
    protected static function onBeforeDelete($object)
    {
        if ($object->child || Article::whereIn('catid', $object->arrchildid)->value('id')) {
            $object->error = '该分类存在数据或子分类';
            return false;
        }
        return true;
    }

    /**
     * 删除后
     * @param static $object
     * @throws DataNotFoundException
     * @throws DbException
     * @throws InvalidArgumentException
     * @throws ModelNotFoundException
     */
    protected static function onAfterDelete($object)
    {
        // 更新缓存
        self::info($object->id, '', true);
        self::getAllName(false);

        // 投递异步任务
        self::deliverTask($object);
    }

    /**
     * status 获取器
     * @return string
     */
    protected function getStatusNameAttr(): string
    {
        return self::$statustxt[$this->status] ?? '未知';
    }

    /**
     * 获得所有父栏目名称
     * @return array
     */
    protected function getArrParentNameAttr(): array
    {
        $strArr = [];
        $categoryList = self::getAllName();
        $arrparentid = explode(',', $this->arrparentid);
        foreach ($arrparentid as $v) {
            if (isset($categoryList[$v])) {
                $strArr[$v] = $categoryList[$v];
            }
        }
        return $strArr;
    }

    /**
     * 添加
     * @param array $data 数据
     * @return bool
     */
    public function plus(array $data): bool
    {
        if (empty($data)) {
            $this->error = "数据不能为空";
            return false;
        }
        $validate = Validate::rule(
            [
                'name|栏目名称' => ['require', function ($name, $data) {
                    $exists = self::where('name', $name)
                        ->where('parentid', $data['parentid'] ?? 0)
                        ->value('id');
                    if ($exists) {
                        return '已经存在同级栏目名称';
                    }
                    return true;
                }],
                'parentid|父栏目' => ['number', function ($parentid, $data) {
                    // 为空表示一级分类
                    if ((int)$parentid == 0 || !isset($data['parentid'])) {
                        return true;
                    }
                    $exists = self::where('id', $parentid)->value('id');
                    if (!$exists) {
                        return '父分类不存在';
                    }
                    $isArticle = Article::withTrashed()->where('catid', $parentid)->value('id');
                    if ($isArticle) {
                        return '父分类下存在文章';
                    }
                    return true;
                }],
            ]
        );
        if (!$validate->check($data)) {
            $this->error = $validate->getError() ?: '验证失败';
            return false;
        }
        return $this->save($data);
    }

    /**
     * 编辑
     * @param array $data 数据
     * @return bool
     */
    public function edit(array $data): bool
    {
        if (empty($data)) {
            $this->error = '数据不能为空!';
            return false;
        }
        $validate = Validate::rule(
            [
                'name|栏目名称' => [function ($name, $data) {
                    if (!isset($data['name'])) {
                        return true;
                    }
                    $exists = self::where('name', $name)
                        ->where('parentid', $data['parentid'] ?? 0)
                        ->where('id', '<>', $this->id)
                        ->value('id');
                    if ($exists) {
                        return '已经存在同级栏目名称';
                    }
                    return true;
                }],
                'parentid|父栏目' => ['number', function ($parentid, $data) {
                    // 为空表示一级分类
                    if (!isset($data['parentid']) || (int)$parentid == 0) {
                        return true;
                    }
                    $exists = self::where('id', $parentid)->value('id');
                    if (!$exists) {
                        return '父分类不存在';
                    }
                    $isArticle = Article::withTrashed()->where('catid', $parentid)->value('id');
                    if ($isArticle) {
                        return '父分类下存在文章';
                    }
                    // 编辑时处理，所选择的父分类不能放在当前分类的子分类下
                    $childList = self::getArrchildid($this->id);
                    if (in_array($parentid, explode(',', $childList))) {
                        return '父分类选择错误';
                    }
                    return true;
                }],
            ]
        );
        if (!$validate->check($data)) {
            $this->error = $validate->getError() ?: '验证失败';
            return false;
        }
        return $this->save($data);
    }

    /**
     * 更新修复栏目数据
     * @return false
     * @throws DataNotFoundException
     * @throws DbException
     * @throws Exception
     * @throws ModelNotFoundException
     */
    public function repair(): bool
    {
        // 更新数据
        $data = [
            'arrparentid' => self::getArrparentid($this->id),
            'arrchildid' => self::getArrchildid($this->id),
        ];
        $data['child'] = $data['arrchildid'] ? 1 : 0;
        return $this->save($data);
    }

    /**
     * 投递异步任务
     * @param Category $category
     * @return void
     */
    public static function deliverTask(Category $category)
    {
        $task = [
            'id' => $category->id,
        ];
        Queue::connection()->push('article.Category', $task);
    }

    /**
     * 获取父分类ID列表
     * @param int $catid 分类ID
     * @return string
     * @throws Exception
     */
    public static function getArrparentid(int $catid)
    {
        $categorys = self::field('id,parentid')
            ->order(['listorder' => 'desc', 'id' => 'asc'])
            ->cache(3)
            ->column('id,parentid,listorder', 'id');
        $parentIds = [];
        $currentId = $catid;
        // 防止循环引用
        $visited = [];
        // 在生产环境中可添加深度保护
        $maxDepth = 20;
        $depth = 0;
        do {
            $depth++;
            if ($depth > $maxDepth) {
                throw new Exception("最大父级深度（{$maxDepth}）超出限制");
            }
            $parentId = (int)$categorys[$currentId]['parentid'] ?? 0;
            if ($parentId === 0) {
                break;
            }
            // 检测循环引用
            if (in_array($parentId, $visited, true)) {
                throw new Exception("检测到ID处的类别循环: {$currentId}");
            }
            $visited[] = $parentId;
            $parentIds[] = $parentId;
            $currentId = $parentId;
        } while (true);
        return implode(',', $parentIds);
    }

    /**
     * 获取子分类ID列表
     * @param int $catid 分类id，以逗号隔开
     * @return string 返回分类子列表，以逗号隔开
     * @throws DataNotFoundException
     * @throws DbException
     * @throws Exception
     * @throws ModelNotFoundException
     */
    public static function getArrchildid(int $catid): string
    {
        $cachedTree = [];
        $list = self::field('id,parentid')
            ->order(['listorder' => 'desc', 'id' => 'asc'])
            ->cache(3)
            ->select();
        foreach ($list as $item) {
            $cachedTree[$item->parentid][] = $item->id;
        }
        // 层级深度保护
        $depth = 0;
        $result = [];
        $stack = [$catid];
        // 非递归深度优先遍历
        while (!empty($stack) && $depth++ < 1000) {
            $currentParent = array_pop($stack);
            if (isset($cachedTree[$currentParent])) {
                foreach ($cachedTree[$currentParent] as $childId) {
                    $result[] = $childId;
                    $stack[] = $childId;
                }
            }
        }
        if ($depth >= 1000) {
            throw new Exception('类别层级过深');
        }
        return implode(',', $result);
    }

    /**
     * 获取栏目相关信息
     * @param int $catid 栏目id
     * @param string|null $field 返回的字段，默认返回全部，数组
     * @param bool $newCache 是否强制刷新
     * @return array|bool|mixed|Model|null
     * @throws InvalidArgumentException
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public static function info(int $catid, ?string $field = '', bool $newCache = false)
    {
        if (empty($catid)) {
            return false;
        }
        $cacheKeyName = "category/info/{$catid}";
        // 强制刷新缓存
        if ($newCache) {
            self::master()
                ->getConnection()
                ->getCache()
                ->delete($cacheKeyName);
        }
        $cache = self::cacheAlways($cacheKeyName, 3600)->find($catid);
        if (empty($cache)) {
            return false;
        }
        if ($field) {
            return Arr::get($cache->toArray(), $field, null);
        } else {
            return $cache;
        }
    }

    /**
     * 获得所有分类名
     * @param bool $isCache 是否走缓存
     * @return array
     */
    public static function getAllName(bool $isCache = true): array
    {
        $cacheKeyName = 'category/list';
        $query = self::where('status', 1);
        if ($isCache) {
            $query->cache($cacheKeyName);
        } else {
            $query->cacheForce($cacheKeyName);
        }
        return $query->column('name', 'id');
    }
}
