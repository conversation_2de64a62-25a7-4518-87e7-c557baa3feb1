<?php
// +----------------------------------------------------------------------
// | ArticleContent 文章内容
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\model\article;

use app\model\BaseModel;
use common\traits\model\CacheInfoTraits;
use Psr\SimpleCache\InvalidArgumentException;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\model\relation\BelongsTo;

/**
 * Class app\model\article\ArticleContent
 * @property int $aid 文章ID
 * @property int $catid 栏目ID
 * @property string $body 文章内容
 * @property-read \app\model\article\Article $article
 */
class ArticleContent extends BaseModel
{
    use CacheInfoTraits;

    /**
     * 配置
     * @return array
     */
    protected function getOptions(): array
    {
        return [
            'type' => [
                'aid' => 'integer',
                'catid' => 'integer',
            ],
            'defaultSoftDelete' => 0,
        ];
    }

    /**
     * 相对关联 article
     * @return BelongsTo
     */
    public function article()
    {
        return $this->belongsTo(Article::class, 'id', 'aid');
    }

    /**
     * 写入后
     * @param static $object
     * @throws InvalidArgumentException
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected static function onAfterWrite($object)
    {
        self::info($object->aid, false);
    }
}
