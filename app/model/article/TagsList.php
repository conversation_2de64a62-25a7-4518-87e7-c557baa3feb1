<?php
// +----------------------------------------------------------------------
// | TagsList 标签数据
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\model\article;

use app\model\BaseModel;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\model\relation\HasOne;

/**
 * Class app\model\article\TagsList
 * @property int $aid 文章ID
 * @property int $catid 栏目id
 * @property int $id
 * @property int $tag_id 标签ID
 * @property string $create_time 创建时间
 * @property string $description 简介
 * @property string $tag_name 标签名
 * @property string $thumb 缩略图
 * @property string $title 标题
 * @property string $update_time 修改时间
 * @property-read \app\model\article\Tags $tag
 * @property-read string $pic
 */
class TagsList extends BaseModel
{
    /**
     * 配置
     * @return array
     */
    protected function getOptions(): array
    {
        return [
            'type' => [
                'tag_id' => 'integer',
                'aid' => 'integer',
                'catid' => 'integer',
                'data_spread' => 'array',
                'update_time' => 'integer',
                'create_time' => 'integer',
            ],
            'defaultSoftDelete' => 0,
        ];
    }

    /**
     * 一对一关联 标签
     * @return HasOne
     */
    public function tag()
    {
        return $this->hasOne(Tags::class, 'id', 'tag_id');
    }

    /**
     * 获取不带后缀的图片地址
     * @return string
     */
    protected function getPicAttr(): string
    {
        $pic = $this->getData('pic');
        if (empty($pic)) {
            return '';
        }
        $parseUrl = parse_url($pic);
        if (!isset($parseUrl['host'])) {
            return '';
        }
        return $parseUrl['scheme'] . '://' . $parseUrl['host'] . $parseUrl['path'];
    }

    /**
     * 标签数据更新
     * @param Article $object
     * @return bool
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public static function tagsUpdate(Article $object): bool
    {
        if (!isset($object['tags']) || empty($object->id)) {
            return false;
        }
        $tags = trim($object['tags']);
        // 将空格，中文逗号都转义为英文逗号
        $updataTagsArr = array_filter(explode(',', str_replace(['，', ' '], ',', $tags)));
        // 如果为空,清除标签数据
        if (empty($updataTagsArr)) {
            self::where('aid', $object->id)
                ->where('catid', $object->catid)
                ->delete();
            return true;
        }
        $updataTags = [];
        foreach ($updataTagsArr as $v) {
            $updataTags[] = trim(mb_substr(trim($v), 0, 255));
        }
        // 标签数据
        $data = [
            'aid' => $object->id,
            'catid' => $object['catid'],
            'title' => $object->getData('title'),
            'thumb' => $object->thumb,
            'description' => $object->description,
        ];
        // 现有标签数据
        $oldTagList = self::where('aid', $object->id)
            ->where('catid', $object->catid)
            ->column('tag_name');

        $updata = array_intersect($updataTags, $oldTagList);
        $add = array_diff($updataTags, $oldTagList);
        $delete = array_diff($oldTagList, $updataTags);
        // 新增标签
        Tags::tagsUpdates($add);
        // 新增标签数据
        if (!empty($add)) {
            // 获取标签信息
            $tagList = Tags::whereIn('name', $add)
                ->column('id', 'name');
            $addArr = [];
            foreach ($add as $name) {
                $addArr[] = array_merge(['tag_id' => $tagList[$name], 'tag_name' => $name], $data);
            }
            $model = new static();
            $model->saveAll($addArr);
        }
        // 更新标签数据
        if (!empty($updata)) {
            foreach ($updata as $name) {
                self::where('tag_name', $name)
                    ->where('aid', $object->id)
                    ->where('catid', $object->catid)
                    ->update($data);
            }
        }
        // 删除标签
        if (!empty($delete)) {
            self::where('tag_name', 'in', $delete)
                ->where('aid', $object->id)
                ->where('catid', $object->catid)
                ->delete();
        }
        return true;
    }
}
