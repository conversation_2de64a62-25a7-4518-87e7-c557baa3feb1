<?php
// +----------------------------------------------------------------------
// | Article 文章
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\model\article;

use app\interfaces\PositionDataInterface;
use app\model\BaseModel;
use app\traits\model\BackgroundOperatorTraits;
use common\traits\model\BaseDeleteTraits;
use common\traits\model\CacheInfoTraits;
use Psr\SimpleCache\InvalidArgumentException;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\Exception;
use think\facade\Validate;
use think\helper\Str;
use think\model\concern\SoftDelete;
use think\model\relation\HasMany;
use think\model\relation\HasOne;

/**
 * Class app\model\article\Article
 * @property int $admin_uid 后台用户id
 * @property int $catid 栏目
 * @property int $delete_time 删除时间
 * @property int $id 信息ID
 * @property int $is_thumb 是否存在缩略图
 * @property int $posid 是否有加推荐位
 * @property int $release_time 发布时间
 * @property int $status 状态，0待审核，1预发布，99审核
 * @property int $views 点击总数
 * @property string $admin_username 后台用户名
 * @property string $author 作者
 * @property string $create_time 创建时间
 * @property string $description 简介
 * @property string $keywords 关键字，多个用,分隔
 * @property string $posid_ids 加入的推荐位id列表
 * @property string $short_title 短标题
 * @property string $source 文章来源
 * @property string $tags tag标签，多个用,分隔
 * @property string $thumb 缩略图
 * @property string $title 标题
 * @property string $update_time 修改时间
 * @property-read \app\model\article\ArticleContent $content
 * @property-read \app\model\article\Category $category
 * @property-read \app\model\article\PositionData[] $position_data
 * @property-read \app\model\article\TagsList[] $tags_list
 * @property-read string $status_name
 * @method static \think\db\Query onlyTrashed()
 * @method static \think\db\Query withTrashed()
 */
class Article extends BaseModel implements PositionDataInterface
{
    use SoftDelete;
    use BaseDeleteTraits;
    use CacheInfoTraits;
    use BackgroundOperatorTraits;

    /**
     * 文章状态列表
     * @var string[]
     */
    public static array $statustxt = [
        99 => '审核',
        0 => '待审核',
        1 => '预发布',
    ];

    /**
     * 配置
     * @return array
     */
    protected function getOptions(): array
    {
        return [
            'type' => [
                'catid' => 'integer',
                'is_thumb' => 'integer',
                'status' => 'integer',
                'views' => 'integer',
                'release_time' => 'integer',
                'create_time' => 'integer',
                'update_time' => 'integer',
                'delete_time' => 'integer',
            ],
            'defaultSoftDelete' => 0,
        ];
    }

    /**
     * 一对一关联文章内容
     * @return HasOne
     */
    public function content()
    {
        return $this->hasOne(ArticleContent::class, 'aid', 'id')->bind(['body']);
    }

    /**
     * 一对一关联文章栏目
     * @return HasOne
     */
    public function category()
    {
        return $this->hasOne(Category::class, 'id', 'catid');
    }

    /**
     * 一对多关联标签数据
     * @return HasMany
     */
    public function tagsList()
    {
        return $this->hasMany(TagsList::class, 'aid', 'id');
    }

    /**
     * 一对多关联推荐位数据
     * @return HasMany
     */
    public function positionData()
    {
        return $this->hasMany(PositionData::class, 'original_id', 'id')->where(['type' => 1]);
    }

    /**
     * 新增前
     * @param static $object
     */
    protected static function onBeforeInsert($object)
    {
        $changedData = $object->getChangedData();
        // 是否有推荐位
        if (isset($changedData['posid_ids'])) {
            $object->posid = $object->posid_ids ? 1 : 0;
        }
        // 是否有缩略图
        if (isset($changedData['thumb'])) {
            $object->is_thumb = $object->thumb ? 1 : 0;
        }
        $object->admin_uid = $object->adminUid;
        $object->admin_username = $object->adminUsername;
    }

    /**
     * 新增后
     * @param static $object
     * @throws DataNotFoundException
     * @throws DbException
     * @throws Exception
     * @throws InvalidArgumentException
     * @throws ModelNotFoundException
     * @throws \Throwable
     */
    protected static function onAfterInsert($object)
    {
        $changedData = $object->getChangedData();
        // tag处理
        if (isset($changedData['tags'])) {
            $object->tagsHandle();
        }
        // 推荐位处理
        if (isset($changedData['posid_ids'])) {
            $object->posidHandle();
        }
    }

    /**
     * 更新前
     * @param static $object
     */
    protected static function onBeforeUpdate($object)
    {
        $changedData = $object->getChangedData();
        // 是否有推荐位
        if (isset($changedData['posid_ids'])) {
            $object->posid = $object->posid_ids ? 1 : 0;
        }
        // 是否有缩略图
        if (isset($changedData['thumb'])) {
            $object->is_thumb = $object->thumb ? 1 : 0;
        }
    }

    /**
     * 更新后
     * @param static $object
     * @throws DataNotFoundException
     * @throws DbException
     * @throws Exception
     * @throws InvalidArgumentException
     * @throws ModelNotFoundException
     * @throws \Throwable
     */
    protected static function onAfterUpdate($object)
    {
        $changedData = $object->getChangedData();
        // tag处理
        if (isset($changedData['tags'])) {
            $object->tagsHandle();
        }
        // 推荐位处理
        if (isset($changedData['posid_ids'])) {
            $object->posidHandle();
        }
    }

    /**
     * 写入后
     * @param static $object
     * @throws InvalidArgumentException
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected static function onAfterWrite($object)
    {
        // 更新缓存
        self::info($object->id, false);
    }

    /**
     * 删除后
     * @param static $object
     * @throws DataNotFoundException
     * @throws DbException
     * @throws InvalidArgumentException
     * @throws ModelNotFoundException
     */
    protected static function onAfterDelete($object)
    {
        // tag标签信息删除
        foreach ($object->tags_list as $each) {
            /**
             * @var $each TagsList
             */
            $each->delete();
        }
        // 推荐位删除
        foreach ($object->position_data as $each) {
            /**
             * @var $each PositionData
             */
            $each->delete();
        }
        // 更新缓存
        self::info($object->id, false);
    }

    /**
     * 获取状态文字
     * @return string
     */
    protected function getStatusNameAttr(): string
    {
        return self::$statustxt[$this->status] ?? '未知';
    }

    /**
     * 设置来源
     * @param $value
     * @return string
     */
    protected function setSourceAttr($value): string
    {
        if (empty($value)) {
            $value = '乾元日历';
        }
        return trim($value);
    }

    /**
     * 设置标签
     * @param $value
     * @return string
     */
    protected function setTagsAttr($value): string
    {
        $value = str_replace(['，'], ',', $value);
        return trim(trim($value), ',');
    }

    /**
     * 设置关键词
     * @param $value
     * @return string
     */
    protected function setKeywordsAttr($value): string
    {
        $value = str_replace('，', ',', $value);
        return trim(trim($value), ',');
    }

    /**
     * 设置推荐位id列表
     * @param $value
     * @return string
     */
    protected function setPosidIdsAttr($value): string
    {
        $value = str_replace(['，'], ',', $value);
        return trim(trim($value), ',');
    }

    /**
     * thumb 修改
     * @param $value
     * @param $data
     * @return string
     */
    protected function setThumbAttr($value, $data): string
    {
        if (!empty($value)) {
            return $value;
        }
        $content = $data['body'] ?? '';
        $imgs = $this->getImgsList($content);
        if (empty($imgs)) {
            return '';
        }
        // 从内容获取第一张图片替换上去
        return $imgs[0];
    }

    /**
     * release_time 修改器
     * @param $value
     * @return int
     */
    protected function setReleaseTimeAttr($value): int
    {
        if (is_string($value)) {
            $value = strtotime($value);
        }
        return $value;
    }

    /**
     * 简介处理
     * @param $value
     * @param $data
     * @return string
     */
    protected function setDescriptionAttr($value, $data): string
    {
        if (empty($value) && isset($data['body'])) {
            $content = str_replace(["\r", "\n", " "], '', strip_tags($data['body']));
            return Str::substr($content, 0, 200);
        }
        return $value;
    }

    /**
     * 添加数据
     * @param array $data
     * @return bool
     */
    public function plus(array $data): bool
    {
        if (empty($data)) {
            $this->error = '数据不能为空';
            return false;
        }
        $validate = Validate::rule(
            [
                'catid|分类' => ['require', 'number', 'gt:0', function ($value) {
                    $cat = Category::info($value);
                    if (empty($cat)) {
                        return '分类不存在';
                    }
                    if ($cat->child) {
                        return '该分类无法添加文章';
                    }
                    return true;
                }],
                'title|标题' => ['require', 'unique:article'],
                'body|正文' => ['require'],
            ]
        );
        if (!$validate->check($data)) {
            $this->error = $validate->getError() ?: '验证失败';
            return false;
        }
        self::startTrans();
        try {
            if ($this->together(['content' => ['body', 'catid']])->save($data)) {
                self::commit();
                return true;
            }
        } catch (\Exception | \Throwable $e) {
            $this->error = $e->getMessage();
        }
        self::rollback();
        return false;
    }

    /**
     * 编辑
     * @param array $data
     * @return bool
     */
    public function edit(array $data): bool
    {
        if (empty($data)) {
            $this->error = '数据不能为空';
            return false;
        }
        $validate = Validate::rule(
            [
                'catid|分类' => ['number', 'gt:0', function ($value, $data) {
                    if (!isset($data['catid'])) {
                        return true;
                    }
                    $cat = Category::info($value);
                    if (empty($cat)) {
                        return '分类不存在';
                    }
                    if ($cat->child) {
                        return '该分类无法添加文章';
                    }
                    return true;
                }],
                'title|标题' => ["unique:article,title,{$this->id}"],
            ]
        );
        if (!$validate->check($data)) {
            $this->error = $validate->getError() ?: '验证失败';
            return false;
        }
        $together = [];
        if (isset($data['body'])) {
            $together['content'][] = 'body';
        }
        self::startTrans();
        try {
            if ($this->together($together)->save($data)) {
                self::commit();
                return true;
            }
        } catch (\Exception | \Throwable $e) {
            $this->error = $e->getMessage();
        }
        self::rollback();
        return false;
    }

    /**
     * 软删除恢复
     * @return bool
     */
    public function rmRestore()
    {
        self::startTrans();
        try {
            if ($this->restore()) {
                $this->tagsHandle();
                $this->posidHandle();
                self::commit();
                return true;
            }
        } catch (\Exception | \Throwable $e) {
            $this->error = $e->getMessage();
        }
        self::rollback();
        return false;
    }

    /**
     * 生成推荐位数据
     * @return array
     */
    public function generatePositionData(): array
    {
        // 基本数据
        $posData = [
            'title' => $this->title,
            'original_id' => $this->id,
            'original_catid' => $this->catid,
            'type' => 1,
            'is_thumb' => $this->is_thumb,
            'thumb' => $this->thumb,
        ];
        return array_merge($this->toArray(), $posData);
    }

    /**
     * 推荐位数据处理
     * @throws DataNotFoundException
     * @throws DbException
     * @throws Exception
     * @throws InvalidArgumentException
     * @throws ModelNotFoundException
     * @throws \Throwable
     */
    protected function posidHandle()
    {
        $posidArr = explode(',', $this->posid_ids);
        $changedData = $this->getChangedData();
        if (
            // 状态
            (isset($this->status) && $this->status != 99) ||
            (isset($changedData['status']) && $changedData['status'] != 99) ||
            // 取消推荐位
            (isset($changedData['posid']) && !$changedData['posid'])
        ) {
            // 删除推荐位数据
            $this->positionData()->delete();
            return;
        }
        if (true !== Position::saveData($posidArr, $this)) {
            throw new Exception('推荐位数据处理失败');
        }
    }

    /**
     * 标签处理
     * @return void
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected function tagsHandle()
    {
        // 更新tag
        if ($this->status == 99) {
            TagsList::tagsUpdate($this);
        } else {
            foreach ($this->tags_list as $each) {
                $each->delete();
            }
        }
    }

    /**
     * 从内容解析出图片列表
     * @param string $content
     * @return array
     */
    protected function getImgsList(string $content): array
    {
        if (empty($content)) {
            return [];
        }
        $imgSrcArr = [];
        // 首先将富文本字符串中的 img 标签进行匹配
        $patternImgTag = '/<img\b.*?(?:\>|\/>)/i';
        preg_match_all($patternImgTag, $content, $matchIMG);
        if (isset($matchIMG[0])) {
            foreach ($matchIMG[0] as $key => $imgTag) {
                // 进一步提取 img标签中的 src属性信息
                $pattern_src = '/\bsrc\b\s*=\s*[\'\"]?([^\'\"]*)[\'\"]?/i';
                preg_match_all($pattern_src, $imgTag, $matchSrc);
                if (isset($matchSrc[1])) {
                    foreach ($matchSrc[1] as $src) {
                        // 将匹配到的src信息压入数组
                        $imgSrcArr[] = $src;
                    }
                }
            }
        }
        return $imgSrcArr;
    }
}
