<?php
// +----------------------------------------------------------------------
// | 万年历-文章
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\model\hz\wnl;

use app\model\BaseModel;
use common\traits\model\BaseDeleteTraits;
use common\traits\model\CacheInfoTraits;
use think\facade\Validate;
use think\helper\Str;
use think\model\concern\SoftDelete;

/**
 * Class app\model\hz\wnl\Article
 * @property int $category 类别ID
 * @property int $delete_time 删除时间
 * @property int $id
 * @property int $release_time 发布时间
 * @property int $status 状态：0待审核，1审核
 * @property string $author 作者
 * @property string $content 内容
 * @property string $coverphoto 封面图片
 * @property string $create_time 创建时间
 * @property string $source 来源
 * @property string $title 标题
 * @property string $update_time 更新时间
 * @property string $url URL
 * @property-read string $category_name
 * @property-read string $status_name
 * @method static \think\db\Query onlyTrashed()
 * @method static \think\db\Query withTrashed()
 */
class Article extends BaseModel
{
    use SoftDelete;
    use BaseDeleteTraits;
    use CacheInfoTraits;

    /**
     * 类别列表
     * @var array
     */
    public static array $categoryList = [
        0 => '未知',
        1 => '生肖运势',
        2 => '星座运势',
        3 => '老黄历',
    ];

    /**
     * 配置
     * @return array
     */
    protected function getOptions(): array
    {
        return [
            'name' => 'HzWnlArticle',
            'type' => [
                'category' => 'integer',
                'status' => 'integer',
                'release_time' => 'integer',
                'create_time' => 'integer',
                'update_time' => 'integer',
            ],
            'defaultSoftDelete' => 0,
            'append' => ['category_name'],
        ];
    }

    /**
     * 新增前
     * @param static $object
     * @return void|bool
     */
    protected static function onBeforeInsert($object)
    {
        if (empty($object->content)) {
            $object->error = '内容不能为空';
            return false;
        }
        if (empty($object->title)) {
            $str = str_replace(["\n", "\r", "\t"], '', $object->content);
            $object->title = Str::substr($str, 0, 30);
        }
    }

    /**
     * 类别名称
     * @return string
     */
    protected function getCategoryNameAttr()
    {
        return self::$categoryList[$this->category] ?? '未知';
    }

    /**
     * 状态名称获取器
     * @return string
     */
    protected function getStatusNameAttr()
    {
        return match ($this->status) {
            0 => '待审核',
            1 => '已审核',
            default => '未知'
        };
    }

    /**
     * 发布时间修改器
     * @param int|string $value
     * @return int
     */
    protected function setReleaseTimeAttr($value)
    {
        if (!is_numeric($value)) {
            $value = strtotime($value);
        }
        return (int)$value;
    }

    /**
     * 添加
     * @param array $data
     * @return bool
     */
    public function plus(array $data): bool
    {
        if (empty($data)) {
            $this->error = '数据不能为空';
            return false;
        }
        $validate = Validate::rule(
            [
                'category|类别' => ['require', function ($value, $data) {
                    if (!isset(self::$categoryList[$value])) {
                        return '类别不存在';
                    }
                    return true;
                }],
                'title|标题' => ['require', 'length:10,50'],
                'status|状态' => ['in:0,1'],
                'url|URL地址' => ['url'],
                'author|作者' => ['require'],
                'source|来源' => ['in:乾元日历,网络'],
                'content|内容' => ['require'],
                'release_time|发布时间' => ['require', 'dateFormat:Y-m-d H:i:s'],
            ]
        );
        if (!$validate->check($data)) {
            $this->error = $validate->getError() ?: '参数验证错误';
            return false;
        }
        return $this->save($data);
    }

    /**
     * 编辑
     * @param array $data
     * @return bool
     */
    public function edit(array $data): bool
    {
        if (empty($data)) {
            $this->error = '数据不能为空';
            return false;
        }
        $validate = Validate::rule(
            [
                'category|类别' => [function ($value, $data) {
                    if (isset($data['category']) && !isset(self::$categoryList[$value])) {
                        return '类别不存在';
                    }
                    return true;
                }],
                'title|标题' => ['length:10,50'],
                'status|状态' => ['in:0,1'],
                'url|URL地址' => ['url'],
                'source|来源' => ['in:乾元日历,网络'],
                'release_time|发布时间' => ['dateFormat:Y-m-d H:i:s'],
            ]
        );
        if (!$validate->check($data)) {
            $this->error = $validate->getError() ?: '参数验证错误';
            return false;
        }
        return $this->save($data);
    }
}
