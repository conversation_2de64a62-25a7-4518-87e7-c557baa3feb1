<?php
// +----------------------------------------------------------------------
// | 事件
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

use think\facade\App;
use think\facade\Env;
use think\swoole\Manager;

return [
    'bind' => [
    ],
    'listen' => [
        'AppInit' => [],
        'HttpRun' => [],
        'HttpEnd' => [],
        'LogLevel' => [],
        'LogWrite' => [],
        'swoole.init' => [function (Manager $manager) {
            $manager->addWorker(function () {
                // 如果是本地调试模式，不运行该事件
                if (Env::get('app_debug', false)) {
                    // 测试站下
                    if (!Env::get('app_test_event', false)) {
                        return;
                    }
                }
                // 正式环境测试机不运行
                if (Env::get('app_formal', false)) {
                    return;
                }
                $timerList = [
                    // redis控制并发，集合数据清理
                    \app\event\other\Concurrency::class,

                    // 文章模块
                    \app\event\article\PreRelease::class,
                ];
                foreach ($timerList as $class) {
                    App::invoke([$class, 'handle']);
                }
            }, 'Cron');
        }],
    ],
    'subscribe' => [
    ],
];
