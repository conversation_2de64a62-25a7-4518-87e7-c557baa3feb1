<?php
// +----------------------------------------------------------------------
// | Concurrency 防止并发使用的集合数据清理
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\event\other;

use app\traits\ConcurrencyRedisTraits;
use common\traits\LanRequestTraits;
use Swoole\Timer;
use think\swoole\Manager;

class Concurrency
{
    use LanRequestTraits;
    use ConcurrencyRedisTraits;

    /**
     * 执行
     * @param Manager $manager
     * @return void
     */
    public function handle(Manager $manager)
    {
        $this->concurrencyRedis($manager);
    }

    /**
     * redis并发清理
     * 超过 10 分钟，就会从并发集合里去除
     * @param Manager $manager
     */
    protected function concurrencyRedis(Manager $manager)
    {
        Timer::tick(
            $this->randSecond(30, 60),
            function () use ($manager) {
                $manager->runWithBarrier(
                    [$manager, 'runInSandbox'],
                    function () {
                        $time = time() - 10 * 60;
                        $redis = $this->getConcurrencyRedis();
                        $list = $redis->zRangeByScore($this->getConcurrencySetListKey(), 0, $time);
                        foreach ($list as $key) {
                            $redis->sRem($this->getConcurrencySetKey(), $key);
                            $redis->zRem($this->getConcurrencySetListKey(), $key);
                        }
                    }
                );
            }
        );
    }
}
