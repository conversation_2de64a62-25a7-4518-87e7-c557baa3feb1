<?php
// +----------------------------------------------------------------------
// | PreRelease 预发布
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\event\article;

use app\model\article\Article;
use common\traits\LanRequestTraits;
use Swoole\Timer;
use think\swoole\Manager;

class PreRelease
{
    use LanRequestTraits;

    /**
     * 执行
     * @param Manager $manager
     * @return void
     */
    public function handle(Manager $manager)
    {
        Timer::tick(
            $this->randSecond(30, 60),
            function () use ($manager) {
                $manager->runWithBarrier(
                    [$manager, 'runInSandbox'],
                    function () {
                        Article::where('status', 1)
                            ->where('release_time', '<=', time())
                            ->chunk(20, function ($list) {
                                /**
                                 * @var Article[] $list
                                 */
                                foreach ($list as $article) {
                                    $article->status = 99;
                                    $article->save();
                                }
                            });
                    }
                );
            }
        );
    }
}
