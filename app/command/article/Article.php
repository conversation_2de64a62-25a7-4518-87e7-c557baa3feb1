<?php
// +----------------------------------------------------------------------
// | Article 文章
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\command\article;

use app\model\article\Article as ArticleModel;
use app\traits\VersionConvertTraits;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\input\Option;
use think\console\Output;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\swoole\rpc\concerns\BindRpcClient;

class Article extends Command
{
    use BindRpcClient;
    use VersionConvertTraits;

    /**
     * 配置
     * @return void
     */
    protected function configure()
    {
        // 指令配置
        $this->setName('tool:article:article')
            ->addArgument('act', Argument::REQUIRED, "执行动作")
            ->addOption('id', null, Option::VALUE_OPTIONAL, 'ID', '')
            ->addOption('trans', 't', Option::VALUE_NONE, '启用事务')
            ->setDescription('文章');
    }

    /**
     * 编辑测试
     * php think tool:article:article edit -t
     * @param Input $input
     * @param Output $output
     * @return void
     * @throws \Throwable
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected function edit(Input $input, Output $output)
    {
        $this->prepareRpcClient();
        $trans = $input->getOption('trans');
        $data = [
            // 'catid' => 1,
            // 'title' => '测试文章' . rand(1, 10000),
            // 'short_title' => '短标题' . rand(1, 10000),
            // 'status' => 99,
            // 'tags' => '标签1，标签2',
            // 'keywords' => '关键词1，关键词2',
            // 'thumb' => rand(0, 1) ? 'https://img.qywnl.com/wnl/2025/07/686b6181bcf10.jpg' : '',
            // 'author' => '乾元',
            // 'source' => '腾讯新闻',
            'posid_ids' => '1',
            // 'description' => '简介',
            // 'views' => 123,
            // 'release_time' => time(),
            // 'body' => '内容' . rand(1, 10000),
        ];
        $trans && ArticleModel::startTrans();
        try {
            $article = ArticleModel::where('id', 37)->find();
            $ref = $article->edit($data);
        } catch (\Exception | \Throwable $e) {
            $trans && ArticleModel::rollback();
            throw $e;
        }
        dump($ref, $article, $article->getError());
    }

    /**
     * 添加测试
     * php think tool:article:article add -t
     * @param Input $input
     * @param Output $output
     * @return void
     * @throws \Throwable
     */
    protected function add(Input $input, Output $output)
    {
        $this->prepareRpcClient();
        $trans = $input->getOption('trans');
        $data = [
            'catid' => 1,
            'title' => '测试文章' . rand(1, 10000),
            'short_title' => '短标题' . rand(1, 10000),
            'status' => 99,
            'tags' => '八字，取名',
            'keywords' => '日历，天气',
            'thumb' => rand(0, 1) ? 'https://img.qywnl.com/wnl/2025/07/686b6181bcf10.jpg' : '',
            'author' => '乾元',
            'source' => '腾讯新闻',
            'posid_ids' => '1,2',
            'description' => '简介',
            'views' => 123,
            'release_time' => time(),
            'body' => '内容' . rand(1, 10000),
        ];
        $trans && ArticleModel::startTrans();
        try {
            $article = new ArticleModel();
            $ref = $article->plus($data);
        } catch (\Exception | \Throwable $e) {
            $trans && ArticleModel::rollback();
            throw $e;
        }
        dump($ref, $article, $article->getError());
    }

    /**
     * 删除测试
     * php think tool:article:article delete -t
     * @param Input $input
     * @param Output $output
     * @return void
     * @throws \Throwable
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected function delete(Input $input, Output $output)
    {
        $this->prepareRpcClient();
        $trans = $input->getOption('trans');
        $trans && ArticleModel::startTrans();
        try {
            $article = ArticleModel::where('id', 27)->find();
            $ref = $article->rm();
        } catch (\Exception | \Throwable $e) {
            $trans && ArticleModel::rollback();
            throw $e;
        }
        dump($ref, $article, $article->getError());
    }

    /**
     * 执行
     * @param Input $input
     * @param Output $output
     * @return void
     */
    protected function execute(Input $input, Output $output)
    {
        $act = $input->getArgument('act');
        if (!method_exists($this, $act)) {
            $output->error("{$act} 方法不存在");
            return;
        }
        $this->{$act}($input, $output);
    }
}
