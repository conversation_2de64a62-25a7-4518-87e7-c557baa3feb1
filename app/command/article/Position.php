<?php
// +----------------------------------------------------------------------
// | Position 推荐位
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\command\article;

use app\model\article\PositionData;
use app\traits\VersionConvertTraits;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\input\Option;
use think\console\Output;
use think\db\exception\DbException;
use think\swoole\rpc\concerns\BindRpcClient;

class Position extends Command
{
    use BindRpcClient;
    use VersionConvertTraits;

    /**
     * 配置
     * @return void
     */
    protected function configure()
    {
        // 指令配置
        $this->setName('tool:article:position')
            ->addArgument('act', Argument::REQUIRED, "执行动作")
            ->addOption('id', null, Option::VALUE_OPTIONAL, 'ID', '')
            ->addOption('trans', 't', Option::VALUE_NONE, '启用事务')
            ->setDescription('推荐位');
    }

    /**
     * 数据修复
     * php think tool:article:position again
     * @param Input $input
     * @param Output $output
     * @return void
     * @throws DbException
     */
    protected function again(Input $input, Output $output)
    {
        $this->prepareRpcClient();
        $output->info('开始进行数据修复');
        $i = 1;
        PositionData::chunk(100, function ($list) use ($input, $output, &$i) {
            $output->write("\r<comment>处理进度: {$i}</comment>");
            foreach ($list as $cat) {
                $cat->again();
            }
            $i++;
        });
        $output->writeln("\n");
        $output->info('处理完成');
    }

    /**
     * 执行
     * @param Input $input
     * @param Output $output
     * @return void
     */
    protected function execute(Input $input, Output $output)
    {
        $act = $input->getArgument('act');
        if (!method_exists($this, $act)) {
            $output->error("{$act} 方法不存在");
            return;
        }
        $this->{$act}($input, $output);
    }
}
