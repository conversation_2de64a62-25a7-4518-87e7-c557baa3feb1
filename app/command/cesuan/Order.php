<?php
// +----------------------------------------------------------------------
// | Demo测试
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\command\cesuan;

use app\model\cesuan\Order as <PERSON><PERSON><PERSON><PERSON>rde<PERSON><PERSON>odel;
use app\traits\rpc\CesuanOrderTraits;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\input\Option;
use think\console\Output;
use think\db\exception\DbException;
use think\db\Query;
use think\Exception;
use think\facade\App;
use think\facade\Config;
use think\facade\Db;
use think\facade\Env;
use think\swoole\rpc\concerns\BindRpcClient;

class Order extends Command
{
    use BindRpcClient;
    use CesuanOrderTraits;

    /**
     * 配置
     * @return void
     */
    protected function configure()
    {
        // 指令配置
        $this->setName('cesuan:order')
            ->addArgument('act', Argument::REQUIRED, "执行动作")
            ->addOption('order_sn', null, Option::VALUE_OPTIONAL, '订单编号', '')
            ->addOption('id', null, Option::VALUE_OPTIONAL, 'ID', '')
            ->addOption('uid', null, Option::VALUE_OPTIONAL, 'UID', '')
            ->addOption('limit', null, Option::VALUE_OPTIONAL, '数量', '')
            ->addOption('status', null, Option::VALUE_OPTIONAL, '状态', '')
            ->setDescription('测算-订单');
    }

    /**
     * 单条同步
     * php think cesuan:order sync --order_sn=
     * @param Input $input
     * @param Output $output
     * @return void
     * @throws Exception
     */
    protected function sync(Input $input, Output $output)
    {
        $this->prepareRpcClient();
        $orderSn = $input->getOption('order_sn');
        if (empty($orderSn)) {
            $output->error('订单号不能为空');
            return;
        }
        $order = $this->orderInfo($orderSn);
        dump($order);
        $newOrder = CesuanOrderModel::sync($order);
        dump($newOrder->toArray());
    }

    /**
     * 批量同步
     * php think cesuan:order syncAll --id=0
     * @param Input $input
     * @param Output $output
     * @return void
     * @throws DbException
     */
    protected function syncAll(Input $input, Output $output)
    {
        $this->prepareRpcClient();
        $id = $input->getOption('id') ?: 0;
        $i = 0;
        /**
         * @var Query $query
         */
        $query = CesuanOrderModel::where('id', '>=', $id);
        $query->chunk(100, function ($list) use ($output, &$i) {
            $i++;
            /**
             * @var CesuanOrderModel $order
             */
            foreach ($list as $order) {
                $cesuanOrder = $this->orderInfo($order->order_sn);
                if (empty($cesuanOrder)) {
                    $output->error("{$order->id},{$order->order_sn} 获取订单信息失败");
                    continue;
                }
                CesuanOrderModel::sync($cesuanOrder);
            }
            $output->info("处理第 {$i} 轮");
        },            'id', 'asc');
        $output->info('执行完成');
    }

    /**
     * 同步测试订单
     * php think cesuan:order devOrderSync --status=0 --limit=100 --uid=1002
     * @param Input $input
     * @param Output $output
     * @return void
     * @throws Exception
     */
    protected function devOrderSync(Input $input, Output $output)
    {
        $this->prepareRpcClient();
        if (!App::isDebug()) {
            $output->error('该命令只能开发环境使用');
            return;
        }
        $status = $input->getOption('status');
        $limit = $input->getOption('limit') ?: 100;
        $uid = $input->getOption('uid') ?: 1000;

        $database = Config::get('database', []);
        $database['connections'] ['cesuan'] = [
            // 数据库类型
            'type' => Env::get('database.type', 'mysql'),
            // 服务器地址
            'hostname' => Env::get('database.hostname', '127.0.0.1'),
            // 数据库名
            'database' => 'cesuan',
            // 用户名
            'username' => Env::get('database.username', 'root'),
            // 密码
            'password' => Env::get('database.password', ''),
            // 端口
            'hostport' => Env::get('database.hostport', '3306'),
            // 数据库连接参数
            'params' => [],
            // 数据库编码默认采用utf8
            'charset' => Env::get('database.charset', 'utf8'),
            // 数据库表前缀
            'prefix' => 'bw_',
            // 数据库部署方式:0 集中式(单一服务器),1 分布式(主从服务器)
            'deploy' => 0,
            // 是否严格检查字段是否存在
            'fields_strict' => true,
            // 是否需要断线重连
            'break_reconnect' => true,
            // 监听SQL
            'trigger_sql' => Env::get('app_debug', false),
            // 开启字段缓存
            'fields_cache' => true,
        ];
        Config::set($database, 'database');
        $list = Db::connect('cesuan')
            ->name('order')
            ->where('status', $status)
            ->orderRand()
            ->limit($limit)
            ->select();
        if (empty($list)) {
            $output->error('没有符合数据');
            return;
        }
        foreach ($list as $order) {
            $cesuanOrder = $this->orderInfo($order['order_sn']);
            if (empty($cesuanOrder)) {
                $output->error("{$order['id']},{$order['order_sn']} 获取订单信息失败");
                continue;
            }
            $newOrder = CesuanOrderModel::sync($cesuanOrder);
            $newOrder->bindUid($uid);
        }
        $output->info('完成');
    }

    /**
     * 执行
     * @param Input $input
     * @param Output $output
     * @return void
     */
    protected function execute(Input $input, Output $output)
    {
        $act = $input->getArgument('act');
        if (!method_exists($this, $act)) {
            $output->error("{$act} 方法不存在");
            return;
        }
        $this->{$act}($input, $output);
    }
}
