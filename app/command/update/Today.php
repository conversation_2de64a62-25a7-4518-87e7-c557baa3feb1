<?php
// +----------------------------------------------------------------------
// | Today 历史上的今天数据更新
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\command\update;

use api\build\Curl;
use app\model\tool\Today as TodayModel;
use GuzzleHttp\Exception\GuzzleException;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\input\Option;
use think\console\Output;
use think\swoole\rpc\concerns\BindRpcClient;

class Today extends Command
{
    use BindRpcClient;

    /**
     * 配置
     * @return void
     */
    protected function configure()
    {
        // 指令配置
        $this->setName('update:today')
            ->addArgument('act', Argument::REQUIRED, "执行动作")
            ->addOption('month', null, Option::VALUE_OPTIONAL, '月份', 1)
            ->addOption('day', null, Option::VALUE_OPTIONAL, '几号', 0)
            ->setDescription('历史上的今天');
    }

    /**
     * 通过接口更新数据库
     * php think update:today data --month=1 --day=1
     * @param Input $input
     * @param Output $output
     * @return void
     * @throws GuzzleException
     */
    protected function data(Input $input, Output $output)
    {
        $this->prepareRpcClient();
        $month = $input->getOption('month');
        $day = $input->getOption('day');
        if ($month < 10) {
            $month = "0{$month}";
        }
        if ($day) {
            if ($day < 10) {
                $day = "0{$day}";
            }
            $output->info("开始抓取 {$month}月-{$day}号历史上今天数据");
            $body = Curl::G("https://www.wudada.online/Api/ScLsDay?month={$month}&&day={$day}");
            $data = json_decode($body, true);
            $this->save($month, $day, $data['data']);
        } else {
            $monthToDay = [1 => 31, 2 => 29, 3 => 31, 4 => 30, 5 => 31, 6 => 30, 7 => 31, 8 => 31, 9 => 30, 10 => 31, 11 => 30, 12 => 31];
            for ($i = 1; $i <= $monthToDay[(int)$month]; $i++) {
                $day = $i;
                if ($i < 10) {
                    $day = "0{$i}";
                }
                $output->info("开始抓取 {$month}月-{$day}号历史上今天数据");
                $body = Curl::G("https://www.wudada.online/Api/ScLsDay?month={$month}&&day={$day}");
                $data = json_decode($body, true);
                if (empty($data) || empty($data['code']) || $data['code'] != 200) {
                    $output->error("抓取 {$month}月-{$day}号数据失败");
                    continue;
                }
                $this->save($month, $i, $data['data']);
                sleep(1);
            }
        }
    }

    /**
     * 保存更新数据
     * @param int $month
     * @param int $day
     * @param array $list
     */
    protected function save(int $month, int $day, array $list)
    {
        foreach ($list as $data) {
            $da = [
                'month' => $month,
                'day' => $day,
                'date' => str_replace(['年', '月', '日'], ['-', '-', ''], $data['date']),
                'title' => $data['title'],
            ];
            // 日期处理，可能是一千多年以前的
            $dateArr = explode('-', $da['date']);
            $year = str_pad($dateArr[0], 4, '0', STR_PAD_LEFT);
            $da['date'] = "{$year}-{$dateArr[1]}-{$dateArr[2]}";
            $info = TodayModel::where('month', $month)
                ->where('day', $day)
                ->where('title', $da['title'])
                ->findOrEmpty();
            $info->save($da);
        }
    }

    /**
     * 执行
     * @param Input $input
     * @param Output $output
     * @return void
     */
    protected function execute(Input $input, Output $output)
    {
        $act = $input->getArgument('act');
        if (!method_exists($this, $act)) {
            $output->error("{$act} 方法不存在");
            return;
        }
        $this->{$act}($input, $output);
    }
}
