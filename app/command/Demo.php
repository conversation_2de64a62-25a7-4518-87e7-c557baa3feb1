<?php
// +----------------------------------------------------------------------
// | Demo测试
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\command;

use app\traits\VersionConvertTraits;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\input\Option;
use think\console\Output;
use think\swoole\rpc\concerns\BindRpcClient;

class Demo extends Command
{
    use BindRpcClient;
    use VersionConvertTraits;

    /**
     * 配置
     * @return void
     */
    protected function configure()
    {
        // 指令配置
        $this->setName('tool:demo')
            ->addArgument('act', Argument::REQUIRED, "执行动作")
            ->addOption('id', null, Option::VALUE_OPTIONAL, '订单ID', '')
            ->setDescription('测试');
    }

    /**
     * 测试
     * php think tool:demo demo
     * @param Input $input
     * @param Output $output
     * @return void
     */
    protected function demo(Input $input, Output $output)
    {
        $this->prepareRpcClient();
    }

    /**
     * 执行
     * @param Input $input
     * @param Output $output
     * @return void
     */
    protected function execute(Input $input, Output $output)
    {
        $act = $input->getArgument('act');
        if (!method_exists($this, $act)) {
            $output->error("{$act} 方法不存在");
            return;
        }
        $this->{$act}($input, $output);
    }
}
