<?php

/**
 * This file is auto-generated.
 */

declare(strict_types=1);

namespace rpc\contract\nadm;

use think\swoole\rpc\client\Service;

interface AuthorizeInterface extends Service
{
	/**
	 * 获取token详情
	 * @param string $token 登录token
	 * @return array
	 */
	public function tokenInfo(string $token): array;


	/**
	 * 根据token获取对应的用户信息
	 * @param string $token
	 * @return array
	 */
	public function tokenToUserInfo($token): array;


	/**
	 * 根据token获取已授权信息，用于RBAC认证
	 * @param string $token
	 * @return array
	 */
	public function tokenToAccess($token): array;


	/**
	 * 换取登录token
	 * @param string $secretKey 换取凭证
	 * @return string
	 */
	public function barterLoginToken($secretKey): string;
}

interface MenuInterface extends Service
{
	/**
	 * 获取菜单导航
	 * @param int $menuid 菜单ID
	 * @return array
	 */
	public function getMenu($menuid): array;
}

interface FavoritesInterface extends Service
{
	/**
	 * 是否已经收藏
	 * @param int $uid 用户ID
	 * @param int $mid 菜单ID或者地址
	 * @return bool
	 */
	public function isFavorite($uid, $mid = 0): bool;
}

interface OperationLogInterface extends Service
{
	/**
	 * 记录操作日志
	 * @param int $uid
	 * @param string $app
	 * @param string $controller
	 * @param string $action
	 * @param array $server $_SERVER
	 * @param string $pathinfo
	 * @param string $method 请求方式
	 * @param array $parameters 请求参数
	 * @param array $cookies Cookie
	 * @param string $content
	 * @param array $files
	 * @return bool
	 */
	public function record(
		int $uid,
		string $app,
		string $controller,
		string $action,
		array $server,
		string $pathinfo,
		string $method,
		array $parameters,
		array $cookies = [],
		string $content = '',
		array $files = [],
	): bool;
}

interface ProjectInterface extends Service
{
	/**
	 * 获取项目信息
	 * @param int $id 项目ID
	 * @return array
	 * @throws \Psr\SimpleCache\InvalidArgumentException
	 * @throws \think\db\exception\DataNotFoundException
	 * @throws \think\db\exception\DbException
	 * @throws \think\db\exception\ModelNotFoundException
	 */
	public function info(int $id): array;


	/**
	 * 获取项目列表
	 * @param int $display 显示还是隐藏
	 * @return array
	 */
	public function getTreeArray(int $display = 1): array;
}

interface UserInterface extends Service
{
	/**
	 * 获取用户列表信息
	 * @param int|null $status 状态
	 * @return array
	 */
	public function getlist(?int $status = null): array;


	/**
	 * 根据角色id获得用户列表信息
	 * @param string|int $roleIds 角色id，多个用英文逗号隔开
	 * @param int|null $status 状态
	 * @return array
	 */
	public function getlistByRole($roleIds, ?int $status = null): array;


	/**
	 * 获取用户信息
	 * @param int $uid
	 * @param int|null $status
	 * @return array
	 */
	public function info(int $uid, ?int $status = null): array;
}

interface WxLoginInterface extends Service
{
	/**
	 * 设置扫码得到的openid
	 * @param string $ticket 二维码ticket
	 * @param string $openId 微信OpenId
	 * @return bool
	 */
	public function setScanCodeOpenId($ticket, $openId): bool;
}

interface RbacInterface extends Service
{
	/**
	 * 权限检查
	 * @param string $token 登录token
	 * @param string $map [模块/控制器/]方法
	 * @param array $param 额外参数
	 * @return bool
	 */
	public function isAuthenticate(string $token, string $map, array $param = []): bool;


	/**
	 * 是否超级管理员
	 * @param string $token 登录token
	 * @return bool
	 */
	public function isAdministrator(string $token): bool;
}


namespace rpc\contract\upload;

use think\swoole\rpc\client\Service;

interface UploadInterface extends Service
{
	/**
	 * 单文件上传
	 * @param array $file
	 * @param array $config 配置
	 * @param bool|string|null $respectively 是否启用分片上传
	 * @param array $clientRequest 客户端request信息
	 * @return array
	 */
	public function uploads(array $file, array $config, $respectively = false, array $clientRequest = []): array;


	/**
	 * 文件内容上传
	 * @param string $content 文件内容
	 * @param string $ext 保存后缀
	 * @param array $config 配置
	 * @param array|null $path 0文件名、1文件目录
	 * @param array $clientRequest 客户端request信息
	 * @return array
	 */
	public function saveContent(
		string $content,
		string $ext = 'png',
		array $config = [],
		?array $path = null,
		array $clientRequest = [],
	): array;


	/**
	 * 提前生成文件url地址
	 * @param string $ext 文件后缀
	 * @param array $config 上传配置
	 * @return string 地址
	 * @throws \Throwable
	 */
	public function predictGeneratePath(string $ext = 'png', array $config = []): string;


	/**
	 * 附件关联用户
	 * @param int $aid 附件ID
	 * @param int $uerid 会员ID
	 * @param bool $isadmin true表示后台，false表示其他(前台)
	 * @return bool
	 */
	public function relationUser(int $aid, int $uerid, bool $isadmin = false): bool;


	/**
	 * 获取站点列表
	 * @return array
	 */
	public function getSiteList(): array;


	/**
	 * 查询上传记录
	 * @param array $where 查询条件
	 * @param array $order 排序条件
	 * @param int $limit 数量
	 * @param int $page 分页号
	 * @return array
	 */
	public function querySelect(array $where = [], array $order = [], int $limit = 20, int $page = 1): array;
}

interface OssInterface extends Service
{
	/**
	 * 直接保存内容到oss里
	 * @param string $base64Content 需要保存的内容，base64编码
	 * @param string $object 文件路径
	 * @param array $config 上传配置
	 * @return string
	 * @throws \Throwable
	 */
	public function putObject(string $base64Content, string $object, array $config): string;


	/**
	 * 追加内容的方式上传文件到oss
	 * @param string $base64Content 需要保存的内容，base64编码
	 * @param string $object 文件路径
	 * @param int $appendId 上一次位置
	 * @param array $config 上传配置
	 * @return array
	 * @throws \Throwable
	 */
	public function appendObject(string $base64Content, string $object, int $appendId, array $config): array;
}

interface SmsInterface extends Service
{
	/**
	 * 获取配置
	 * @param null|string $key
	 * @param mixed $default
	 * @return array|string
	 */
	public function config($key = null, $default = null);


	/**
	 * 发送短信
	 * @param int $to 接收方
	 * @param array $message 短信内容，包含 content、template、data
	 * @param array $gateways 网关
	 * @return array
	 */
	public function send($to, array $message, array $gateways = []): array;


	/**
	 * 根据场景进行发送
	 * @param int $to 接收方
	 * @param string $scenes 内置场景
	 * @param array $arr 对应参数，按顺序
	 * @return array
	 */
	public function scenes($to, string $scenes, array $arr): array;
}

interface IpLocationInterface extends Service
{
	/**
	 * 获取 国家、省份、城市
	 * @param string $ip
	 * @return array
	 */
	public function find(string $ip): array;


	/**
	 * 获取 国家、省份、城市 带键名
	 * @param string $ip
	 * @return array
	 */
	public function findMap(string $ip): array;


	/**
	 * 城市的行政区划信息
	 * @return array
	 */
	public function locations(): array;
}

interface EmailInterface extends Service
{
	/**
	 * 根据邮件内容发送邮件
	 * @param array|string $to 接收
	 * @param string $subject 邮件标题
	 * @param string $content 邮件内容，支持html
	 * @param string|null $drive 驱动
	 * @return array
	 * @throws Exception
	 */
	public function send($to, string $subject, string $content, ?string $drive = null);


	/**
	 * 根据内置邮件模板发送邮件
	 * @param array|string $to 接收
	 * @param string $template 邮件模板
	 * @param array $parameter 邮件变量参数
	 * @param string|null $drive 驱动
	 * @return array
	 * @throws Exception
	 */
	public function templateSend($to, string $template, array $parameter, ?string $drive = null);
}


namespace rpc\contract\member;

use think\swoole\rpc\client\Service;

interface ApiInterface extends Service
{
	/**
	 * post方式请求接口
	 * @param string $uri
	 * @param array $data
	 * @return mixed
	 */
	public function post(string $uri, array $data = []);


	/**
	 * get方式请求接口
	 * @param string $uri
	 * @param array $data
	 * @return mixed
	 */
	public function get(string $uri, array $data = []);
}

interface AccessTokenInterface extends Service
{
	/**
	 * 获取令牌信息
	 * @param string $accessToken
	 * @param bool $isCache
	 * @return array
	 */
	public function info(string $accessToken, bool $isCache = true): array;


	/**
	 * 清理用户全部AccessToken
	 * @param int $uid 用户UID
	 * @param int $projectId 项目ID
	 * @return bool
	 */
	public function clearAccessToken(int $uid, int $projectId): bool;


	/**
	 * 生成登陆令牌
	 * @param int $uid 用户uid
	 * @param string $driver 驱动名
	 * @param bool $isLoginOnly 是否唯一登陆
	 * @param int $business 业务ID
	 * @param int $day 有效期几天
	 * @return array
	 */
	public function createAccessToken(int $uid, string $driver, bool $isLoginOnly, int $business, int $day = 5): array;


	/**
	 * 生成登陆令牌
	 * @param int $uid 用户uid
	 * @param int $projectId 项目ID
	 * @param string $driver 驱动名
	 * @param bool $isLoginOnly 是否唯一登陆
	 * @param int $day 有效期几天
	 * @return array
	 */
	public function createToken(
		int $uid,
		int $projectId,
		string $driver = 'Default',
		bool $isLoginOnly = false,
		int $day = 5,
	): array;


	/**
	 * 关联登陆token
	 * @param string $accessToken token
	 * @param int $relationId 需要关联的accessToken主键
	 * @return bool
	 */
	public function relationToken(string $accessToken, int $relationId): bool;


	/**
	 * 删除token
	 * @param string $accessToken
	 * @return bool
	 */
	public function delete(string $accessToken): bool;


	/**
	 * 令牌续期
	 * @param string $accessToken token
	 * @return int
	 */
	public function renewal(string $accessToken): int;


	/**
	 * 刷新令牌，同时返回新令牌
	 * @param string $accessToken token
	 * @return array
	 */
	public function refresh(string $accessToken): array;
}

interface AppInterface extends Service
{
	/**
	 * 获取 App 公众号、小程序、服务号等appid和secret
	 * @param int|string $appid appid或者主键ID
	 * @return array
	 */
	public function info($appid): array;
}

interface HelperInterface extends Service
{
	/**
	 * 微信小程序code换成openid
	 * @param string $code
	 * @param int|string $appid 应用App_ID或主键
	 * @return array
	 */
	public function wxappCodeToOpenid(string $code, $appid): array;


	/**
	 * 检查openid是否已绑定，如果是，返回对应Uid
	 * @param string $openid 第三方授权OpenID
	 * @param string|int $appid 应用App_ID或主键
	 * @return int
	 */
	public function isOpenIdBind(string $openid, $appid): int;
}

interface OauthInterface extends Service
{
	/**
	 * 根据授权信息获取用户信息
	 * @param int $projectId 项目ID
	 * @param string $appid app_id
	 * @param string $openid OpenID
	 * @return array
	 */
	public function userInfo(int $projectId, string $appid, string $openid): array;


	/**
	 * 绑定新的授权
	 * @param int $projectId 项目ID
	 * @param string $appid app_id
	 * @param int $uid 用户UID
	 * @param string $openid OpenID
	 * @return bool
	 */
	public function bind(int $projectId, string $appid, int $uid, string $openid): bool;


	/**
	 * 解绑授权
	 * @param int $projectId 项目ID
	 * @param string $appid app_id
	 * @param int $uid 用户UID
	 * @param string $openid OpenID
	 * @return bool
	 */
	public function unBind(int $projectId, string $appid, int $uid, string $openid): bool;


	/**
	 * 根据用户获取授权信息
	 * @param int $projectId 项目ID
	 * @param string $appid app_id
	 * @param int $uid 用户UID
	 * @return array
	 */
	public function accreditInfo(int $projectId, string $appid, int $uid): array;


	/**
	 * 查询用户全部第三方授权信息
	 * @param int $projectId 项目ID
	 * @param int $uid 用户UID
	 * @return array
	 */
	public function list(int $projectId, int $uid): array;


	/**
	 * 清理用户全部授权
	 * @param int $uid 用户UID
	 * @param int $projectId 项目ID
	 * @return bool
	 */
	public function clearBind(int $uid, int $projectId): bool;
}

interface ChinaRegionInterface extends Service
{
	/**
	 * 获取省份
	 * @param string $province 省份
	 * @return int
	 */
	public function getProvinceId(string $province): int;


	/**
	 * 获取城市
	 * @param string $city 城市
	 * @param int|null $pid 父ID
	 * @return int
	 */
	public function getCityId(string $city, ?int $pid = null): int;


	/**
	 * 获取区/县
	 * @param string $area 区/县
	 * @param int|null $pid 父ID
	 * @return int
	 */
	public function getAreaId(string $area, ?int $pid = null): int;


	/**
	 * 根据中文获得对应的id
	 * @param string $name 地区名
	 * @param int $pid 父id
	 * @param int $level 层级
	 * @return int
	 */
	public function cnNameId(string $name, int $pid, int $level = 1): int;


	/**
	 * 返回Tree使用的数组
	 * @param bool $all 是否返回全部
	 * @return array
	 */
	public function getTreeArray(bool $all = true): array;


	/**
	 * 获得地区详情
	 * @param string $id 地区id
	 * @return array
	 */
	public function info(string $id): array;


	/**
	 * 获取下级数据
	 * @param int $pid 父ID
	 * @return array
	 */
	public function child(int $pid): array;


	/**
	 * 提取地址的省市区
	 * @param string $address 地址
	 * @return array
	 */
	public function getRegion(string $address): array;
}


namespace rpc\contract\api;

use think\swoole\rpc\client\Service;

interface ApiInterface extends Service
{
	/**
	 * 获取授权信息
	 * @param int $appId 授权ID
	 * @return array
	 */
	public function accessInfo(int $appId): array;


	/**
	 * 接口合法性和权限验证
	 * @param int $appId 授权ID
	 * @param string $apiPath 接口
	 * @param array $params 需要带入的计算参数
	 * @param string $clientSign 客户端计算出的sign
	 * @param int $apiVersion 接口版本
	 * @return bool
	 */
	public function apiVerification(
		int $appId,
		string $apiPath,
		array $params,
		string $clientSign,
		int $apiVersion = 1,
	): bool;


	/**
	 * 是否有接口请求权限
	 * @param int $appId 授权ID
	 * @param string $apiPath 接口
	 * @param int $apiVersion 接口版本
	 * @return bool
	 */
	public function isAccess(int $appId, string $apiPath, int $apiVersion = 1): bool;


	/**
	 * 服务端根据参数计算生成sign
	 * @param int $appId 授权ID
	 * @param array $params 需要带入的计算参数
	 * @return string
	 */
	public function generateServerSign(int $appId, array $params): string;
}


namespace rpc\contract\pay;

use think\swoole\rpc\client\Service;

interface OrdersInterface extends Service
{
	/**
	 * 创建支付订单并获取收银台信息
	 * @param string $payMethod 支付驱动类型
	 * @param array $data 支付数据
	 * @param int $payType 支付方式：1=WAP支付(手机H5)、2=电脑支付、3=公众号（微信）、4=小程序（微信、百度）、5=APP
	 * @param string $paySuccessUrl 支付成功跳转地址
	 * @param string $payCancelUrl 取消支付跳转地址
	 * @param string $payFailUrl 支付失败跳转地址
	 * @return string|array|bool
	 */
	public function createOrders(
		string $payMethod,
		array $data,
		int $payType,
		string $paySuccessUrl,
		string $payCancelUrl = '',
		string $payFailUrl = '',
	);


	/**
	 * 创建支付订单并返回订单详情
	 * @param string $payMethod 支付驱动类型
	 * @param array $data 支付数据
	 * @param string $paySuccessUrl 支付成功跳转地址
	 * @param string $payCancelUrl 取消支付跳转地址
	 * @param string $payFailUrl 支付失败跳转地址
	 * @return array
	 */
	public function createOrderInfo(
		string $payMethod,
		array $data,
		string $paySuccessUrl,
		string $payCancelUrl = '',
		string $payFailUrl = '',
	);


	/**
	 * 根据支付订单和支付方式获取支付网关信息
	 * @param string $orderSn 支付订单号
	 * @param int $payType 支付方式：1=WAP支付(手机H5)、2=电脑支付、3=公众号（微信）、4=小程序（微信、百度）、5=APP
	 * @param array|null $requestData 请求数据
	 * @return bool|array
	 */
	public function orderPayment(string $orderSn, int $payType, ?array $requestData = null);


	/**
	 * 获取订单详情
	 * @param string|int $payOrderSn 支付订单号、第三方平台订单号、内部关联ID
	 * @param int $type 1支付单号、2第三方平台订单号、3内部关联ID
	 * @param int $businessTypes 当$type=3的时候需要此参数
	 * @return array 订单信息
	 */
	public function payOrderInfo($payOrderSn, int $type = 0, int $businessTypes = 0): array;


	/**
	 * 获取退款订单详情
	 * 部分退款下有效
	 * @param string|int $ordersn ID或者单号
	 * @return array
	 */
	public function refundOrderInfo($ordersn): array;


	/**
	 * 获取退款订单列表
	 * 部分退款下有效
	 * @param string|int $payOrderSn 支付订单号、第三方平台订单号、内部关联ID
	 * @param int $type 1支付单号、2第三方平台订单号、3内部关联ID
	 * @param int $businessTypes 当$type=3的时候需要此参数
	 * @return array
	 */
	public function refundOrderList($payOrderSn, int $type = 0, int $businessTypes = 0): array;


	/**
	 * 根据支付订单Id和退款原因进行退款
	 * 本rpc接口没有进行第三方退款
	 * 该接口需要取消使用 20210823
	 * @param string|int $moneyOrderId 支付订单id或order_sn
	 * @param string $reason 退款原因
	 * @param array $operaUser 后台用户uid和username
	 * @return bool
	 * @deprecated
	 */
	public function refundStatus($moneyOrderId, string $reason, array $operaUser): bool;
}

interface PaymentMethodInterface extends Service
{
	/**
	 * 获取支付平台列表
	 * @return array
	 */
	public function platformList(): array;


	/**
	 * 获取支付方式信息
	 * @param string|int $id 驱动
	 * @return array
	 */
	public function paymentMethodInfo($id): array;


	/**
	 * 获取支付方式Tree数组
	 * @param bool $isNumber 是否以ID为键值
	 * @param bool $isShowHidden 是否显示隐藏
	 * @return array
	 */
	public function getPaymentMethodTree($isNumber = false, $isShowHidden = false): array;


	/**
	 * 获取业务类型Tree数组
	 * @return array
	 */
	public function getBusinessMethodTree(): array;


	/**
	 * 获得支付方式列表
	 * @param string $status
	 * @return array
	 * @throws \think\db\exception\DataNotFoundException
	 * @throws \think\db\exception\DbException
	 * @throws \think\db\exception\ModelNotFoundException
	 */
	public function getPaymentMethodList($status = ''): array;
}

interface OrdersQueryInterface extends Service
{
	/**
	 * 批量获取订单
	 * @param array $condition 条件
	 * @param string $field 字段
	 * @return array
	 */
	public function getList(array $condition, string $field = 'id'): array;


	/**
	 * 查询搜索条件中最早的数据
	 * @param array $where
	 * @param string $field 最小字段
	 * @return int
	 * @deprecated
	 */
	public function getMinInfo(array $where, string $field = 'payment_time'): int;


	/**
	 * 获取订单列表
	 * @param array $where
	 * @param array $order
	 * @param int $limit
	 * @param string $field
	 * @return array
	 * @deprecated
	 */
	public function getListColumn(array $where, array $order, int $limit, string $field): array;
}

interface RefundInterface extends Service
{
	/**
	 * 更新退款状态
	 * @param string|int $moneyOrderId
	 * @return bool
	 */
	public function renewalRefund($moneyOrderId): bool;


	/**
	 * 申请第三方部分退款(在线退款)
	 * @param string|int $moneyOrderId 支付订单id或order_sn
	 * @param float|int $money 退款金额
	 * @param string $reason 退款原因
	 * @param array $operaUser 后台用户uid和username
	 * @param array $refundData 额外退款数据
	 * @return bool
	 */
	public function applyRefund($moneyOrderId, $money, string $reason, array $operaUser, array $refundData = []): bool;


	/**
	 * 申请第三方全额退款(在线退款)
	 * @param string|int $moneyOrderId 支付订单id或order_sn
	 * @param string $reason 退款原因
	 * @param array $operaUser 后台用户uid和username
	 * @return bool
	 */
	public function applyFullRefund($moneyOrderId, string $reason, array $operaUser): bool;


	/**
	 * 手动退款
	 * @param string|int $moneyOrderId 支付订单id或order_sn
	 * @param string $reason 退款原因
	 * @param array $operaUser 后台用户uid和username
	 * @return bool
	 */
	public function manualRefund($moneyOrderId, string $reason, array $operaUser): bool;
}

interface BusinessTypesInterface extends Service
{
	/**
	 * 获取业务类型列表
	 * @return array
	 */
	public function list(): array;
}

interface NoticeCallbackInterface extends Service
{
	/**
	 * 支付回调通知
	 * @param string $payMethod 支付驱动
	 * @param array $requestData 请求参数
	 * @param bool $isGet 请求方式
	 * @return array|bool|string
	 * @throws \think\Exception
	 */
	public function notify(string $payMethod, array $requestData, bool $isGet = false);
}

interface EasyWeChatInterface extends Service
{
	/**
	 * GET
	 * @param array $feature 0模块，1参数
	 * @param string $url 接口地址
	 * @param array $options 参数
	 * @return array
	 */
	public function get(array $feature, string $url, array $options = []): array;


	/**
	 * POST
	 * @param array $feature 0模块，1参数
	 * @param string $url 接口地址
	 * @param array $options 参数
	 * @return array
	 */
	public function post(array $feature, string $url, array $options = []): array;


	/**
	 * PATCH
	 * @param array $feature 0模块，1参数
	 * @param string $url 接口地址
	 * @param array $options 参数
	 * @return array
	 */
	public function patch(array $feature, string $url, array $options = []): array;


	/**
	 * PUT
	 * @param array $feature 0模块，1参数
	 * @param string $url 接口地址
	 * @param array $options 参数
	 * @return array
	 */
	public function put(array $feature, string $url, array $options = []): array;


	/**
	 * DELETE
	 * @param array $feature 0模块，1参数
	 * @param string $url 接口地址
	 * @param array $options 参数
	 * @return array
	 */
	public function delete(array $feature, string $url, array $options = []): array;


	/**
	 * POST JSON
	 * @param array $feature 0模块，1参数
	 * @param string $url 接口地址
	 * @param array $data 数据
	 * @param array $options 参数
	 * @return array
	 */
	public function postJson(array $feature, string $url, array $data = [], array $options = []): array;


	/**
	 * PATCH JSON
	 * @param array $feature 0模块，1参数
	 * @param string $url 接口地址
	 * @param array $data 数据
	 * @param array $options 参数
	 * @return array
	 */
	public function patchJson(array $feature, string $url, array $data = [], array $options = []): array;


	/**
	 * POST XML
	 * @param array $feature 0模块，1参数
	 * @param string $url 接口地址
	 * @param array $data 数据
	 * @param array $options 参数
	 * @return array
	 */
	public function postXml(array $feature, string $url, array $data = [], array $options = []): array;
}


namespace rpc\contract\cesuan;

use think\swoole\rpc\client\Service;

interface OrderInterface extends Service
{
	/**
	 * 批量获取订单
	 * @param array $condition 条件
	 * @param string $field 字段
	 * @return array
	 */
	public function getList(array $condition, string $field = 'id'): array;


	/**
	 * 获取订单信息
	 * @param string|int $ordersn 订单ID或订单号
	 * @return array
	 */
	public function info($ordersn): array;


	/**
	 * 订单绑定手机号
	 * @param string|int $ordersn 订单ID或订单号
	 * @param string $phone 手机号
	 * @param string $areaCode 国家区号
	 * @return bool
	 */
	public function bindMobilePhone($ordersn, string $phone, string $areaCode = '86'): bool;
}

interface ServerInterface extends Service
{
	/**
	 * 批量获取
	 * @param array $condition 条件
	 * @param string $field 字段
	 * @return array
	 */
	public function getList(array $condition, string $field = 'id'): array;


	/**
	 * 获取服务信息
	 * @param int $id 服务ID
	 * @return array
	 */
	public function info(int $id): array;
}

interface PayInterface extends Service
{
	/**
	 * 接收支付通知
	 * @param array $orderInfo
	 * @return bool
	 */
	public function payNotice(array $orderInfo): bool;
}
return [
	'nadm' => [
		'rpc\contract\nadm\AuthorizeInterface',
		'rpc\contract\nadm\MenuInterface',
		'rpc\contract\nadm\FavoritesInterface',
		'rpc\contract\nadm\OperationLogInterface',
		'rpc\contract\nadm\ProjectInterface',
		'rpc\contract\nadm\UserInterface',
		'rpc\contract\nadm\WxLoginInterface',
		'rpc\contract\nadm\RbacInterface',
	],
	'upload' => [
		'rpc\contract\upload\UploadInterface',
		'rpc\contract\upload\OssInterface',
		'rpc\contract\upload\SmsInterface',
		'rpc\contract\upload\IpLocationInterface',
		'rpc\contract\upload\EmailInterface',
	],
	'member' => [
		'rpc\contract\member\ApiInterface',
		'rpc\contract\member\AccessTokenInterface',
		'rpc\contract\member\AppInterface',
		'rpc\contract\member\HelperInterface',
		'rpc\contract\member\OauthInterface',
		'rpc\contract\member\ChinaRegionInterface',
	],
	'api' => ['rpc\contract\api\ApiInterface'],
	'pay' => [
		'rpc\contract\pay\OrdersInterface',
		'rpc\contract\pay\PaymentMethodInterface',
		'rpc\contract\pay\OrdersQueryInterface',
		'rpc\contract\pay\RefundInterface',
		'rpc\contract\pay\BusinessTypesInterface',
		'rpc\contract\pay\NoticeCallbackInterface',
		'rpc\contract\pay\EasyWeChatInterface',
	],
	'cesuan' => [
		'rpc\contract\cesuan\OrderInterface',
		'rpc\contract\cesuan\ServerInterface',
		'rpc\contract\cesuan\PayInterface',
	],
];