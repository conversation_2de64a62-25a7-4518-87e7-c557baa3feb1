<?php
// +----------------------------------------------------------------------
// | UserInterface
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\rpc\interfaces;

interface UserInterface
{
    /**
     * 用户信息
     * @param int|string $uid uid/token/mobile
     * @return array
     */
    public function info($uid): array;

    /**
     * 复用手机号登录验证码
     * @param string $mobile 手机号
     * @param int $appointCode 验证码
     * @return bool
     */
    public function reuseMobileLoginVerificationCode(string $mobile, int $appointCode): bool;
}
