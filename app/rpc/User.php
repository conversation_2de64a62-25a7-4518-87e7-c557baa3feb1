<?php
// +----------------------------------------------------------------------
// | 用户
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\rpc;

use app\model\user\User as UserModel;
use app\rpc\interfaces\UserInterface;
use app\traits\MobileLoginVerificationCodeTraits;
use pass\exception\AccessTokenException;
use pass\exception\UserDataException;
use pass\traits\TraitsAccessToken;
use think\helper\Str;

class User implements UserInterface
{
    use TraitsAccessToken;
    use MobileLoginVerificationCodeTraits;

    /**
     * 用户信息
     * @param int|string $uid uid/token/mobile
     * @return array
     */
    public function info($uid): array
    {
        $auto = 'uid';
        if (!is_numeric($uid)) {
            try {
                $accessTokenInfo = $this->getAceessTokenInfo($uid);
                $uid = $accessTokenInfo['uid'];
            } catch (AccessTokenException $e) {
                $uid = 0;
            }
        } elseif (Str::length($uid) == 11) {
            $auto = 'mobile';
        }
        try {
            $userInfo = UserModel::getUserInfo($uid, null, $auto);
            $data = $userInfo->toArray();
            unset($data['password']);
            return $data;
        } catch (UserDataException $e) {
        }
        return [];
    }

    /**
     * 复用手机号登录验证码
     * @param string $mobile 手机号
     * @param int $appointCode 验证码
     * @return bool
     */
    public function reuseMobileLoginVerificationCode(string $mobile, int $appointCode): bool
    {
        return $this->verificationCodeSend($mobile, $appointCode, false);
    }
}
