<?php
// +----------------------------------------------------------------------
// | 测算订单
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\rpc;

use app\model\cesuan\Order;
use app\rpc\interfaces\CesuanOrderInterface;
use pass\traits\TraitsAccessToken;
use think\Exception;

class CesuanOrder implements CesuanOrderInterface
{
    use TraitsAccessToken;

    /**
     * 同步更新(不存在会新增)
     * @param int|string $uid 所属uid/token
     * @param array $order 订单数据
     * @return bool
     * @throws Exception
     */
    public function sync($uid, array $order): bool
    {
        if (!is_numeric($uid)) {
            $accessTokenInfo = $this->getAceessTokenInfo($uid);
            $uid = $accessTokenInfo['uid'];
        }
        $order['uid'] = $uid;
        Order::sync($order);
        return true;
    }
}
