<?php
// +----------------------------------------------------------------------
// | ConcurrencyRedisTraits 并发处理
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\traits;

use Redis;
use RedisException;
use think\facade\Cache;

trait ConcurrencyRedisTraits
{
    /**
     * 集合
     * @var string
     */
    private string $concurrencySetKey = 'concurrency';

    /**
     * 集合记录
     * @var string
     */
    private string $concurrencySetListKey = 'concurrency-list';

    /**
     * 判断并发状态
     * @param string $concurrencyKey 并发标记，全局不允许重复
     * @param bool $autoLock 未锁时是否自动上锁
     * @return bool
     * @throws RedisException
     */
    protected function hasConcurrency(string $concurrencyKey, bool $autoLock = true): bool
    {
        $redis = $this->getConcurrencyRedis();
        if ($autoLock) {
            // 向集合添加，能添加成功说明没有锁
            if ($redis->handler()->sAdd($this->getConcurrencySetKey(), $concurrencyKey)) {
                // 记录，方便定时清理
                $redis->handler()->zAdd($this->getConcurrencySetListKey(), time(), $concurrencyKey);
                return false;
            }
            return true;
        } else {
            return $redis->handler()->sIsMember($this->getConcurrencySetKey(), $concurrencyKey);
        }
    }

    /**
     * 解锁并发
     * @param string $concurrencyKey 并发标记
     * @return bool
     * @throws RedisException
     */
    protected function unlockConcurrency(string $concurrencyKey)
    {
        $redis = $this->getConcurrencyRedis();
        $redis->handler()->sRem($this->getConcurrencySetKey(), $concurrencyKey);
        $redis->handler()->zRem($this->getConcurrencySetListKey(), $concurrencyKey);
        return true;
    }

    /**
     * 获取redis
     * @return Redis|\think\Cache
     */
    protected function getConcurrencyRedis()
    {
        /**
         * @var Redis|\think\Cache $redis
         */
        $redis = Cache::store('redis');
        return $redis;
    }

    /**
     * 获取集合名称
     * @return string
     */
    protected function getConcurrencySetKey(): string
    {
        return $this->getConcurrencyRedis()->getCacheKey($this->concurrencySetKey);
    }

    /**
     * 获取集合列表名称
     * @return string
     */
    protected function getConcurrencySetListKey(): string
    {
        return $this->getConcurrencyRedis()->getCacheKey($this->concurrencySetListKey);
    }
}
