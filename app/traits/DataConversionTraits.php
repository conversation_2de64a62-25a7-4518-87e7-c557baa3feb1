<?php
// +----------------------------------------------------------------------
// | 数据转换
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\traits;

trait DataConversionTraits
{
    /**
     * 空数组转换为对象类型
     * @param array $data
     * @return void
     */
    protected function emptyArrayConversionObject(array &$data)
    {
        foreach ($data as $key => &$item) {
            if (!is_array($item)) {
                continue;
            }
            if (!empty($item)) {
                $this->emptyArrayConversionObject($item);
            } else {
                $item = (object)$item;
            }
        }
    }
}
