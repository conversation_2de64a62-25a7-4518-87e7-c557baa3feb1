<?php
// +----------------------------------------------------------------------
// | 版本号转换
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\traits;

use InvalidArgumentException;

trait VersionConvertTraits
{
    /**
     * 版本号转换成数字版本号
     * @param string $version
     * @return int
     */
    public static function versionToNumber(string $version): int
    {
        // 按点号拆分版本号
        $parts = explode('.', $version);

        // 确保版本号是 x.y.z 格式
        if (count($parts) !== 3) {
            throw new InvalidArgumentException('版本号格式必须为 x.y.z');
        }

        // 提取 x, y, z
        $x = (int)$parts[0];
        $y = (int)$parts[1];
        $z = (int)$parts[2];

        // 将 x, y, z 转换为固定位数（x 占 3 位，y 占 3 位，z 占 3 位）
        $xPadded = str_pad($x, 3, '0', STR_PAD_LEFT); // x 补零到 3 位
        $yPadded = str_pad($y, 3, '0', STR_PAD_LEFT); // y 补零到 3 位
        $zPadded = str_pad($z, 3, '0', STR_PAD_LEFT); // z 补零到 3 位

        // 拼接为最终的数字
        $result = $xPadded . $yPadded . $zPadded;

        return (int)$result;
    }

    /**
     * 验证版本号是否符合规则
     * @param string $version
     * @return bool
     */
    public static function isValidVersion(string $version): bool
    {
        if (empty($version)) {
            return false;
        }
        // 使用 sscanf 解析字符串
        $result = sscanf($version, '%d.%d.%d');
        // 检查是否成功解析了 3 个数字
        return count($result) === 3 && !in_array(null, $result, true);
    }
}
