<?php
// +----------------------------------------------------------------------
// | DragTheSort 拖动排序
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\traits;

use think\db\exception\DbException;
use think\Exception;
use think\facade\Db;

trait DragTheSortTraits
{
    /**
     * 计算排序值
     * @param string $table 表
     * @param int $event 1 从上往下 0 从下往上
     * @param int $start 起始位置
     * @param int $end 终点位置
     * @param string $pk 表主键字段名
     * @param string $listorder 排序字段名
     * @param string $order 排序
     * @return array
     * @throws Exception
     */
    protected function dragSort(string $table, int $event, int $start, int $end, string $pk = 'id', string $listorder = 'listorder', string $order = 'listorder desc,id desc'): array
    {
        $location = $this->dragLocation($table, $event, $start, $end, $pk, $listorder);
        $limit = $this->dragLimit($table, $event, $start, $end, $order, $pk);
        // 获取要处理的数据ID列表
        $list = Db::table($table)
            ->order($order)
            ->limit($limit)
            ->column('id');
        // 新的拖拽排序后的数组
        // 删除 起始位置
        unset($list[array_search($start, $list)]);
        // 从上往下
        if ($event) {
            // 将 起始位置 放到结尾
            array_push($list, $start);
        } else {
            // 从下到上 5 拖动到 4，在end前面放入start
            array_splice($list, array_search($end, $list), 0, $start);
        }
        $count = count($list);
        // ID和排序值的映射关系
        $return = [];
        $startSum = $event ? $location[$end] : $location[$start];
        foreach ($list as $index => $id) {
            $sort = $startSum + $count - $index;
            $return[$id] = $sort;
        }
        return $return;
    }

    /**
     * 获取位置数组
     * @param string $table 表
     * @param int $event 1 从上往下 0 从下往上
     * @param int $start 起始位置
     * @param int $end 终点位置
     * @param string $pk 表主键字段名
     * @param string $listorder 排序字段名
     * @return array
     * @throws Exception
     */
    protected function dragLocation(string $table, int $event, int $start, int $end, string $pk = 'id', string $listorder = 'listorder'): array
    {
        // 位置信息
        $location = Db::table($table)
            ->whereIn($pk, [$start, $end])
            ->visible([$pk, $listorder])
            ->column($listorder, $pk);
        if (empty($location)) {
            throw new Exception('起始位置不存在');
        }
        // 验证数据
        if ($event) {
            // 从上往下，起点排序>终点，起点ID>终点
            if ($location[$start] < $location[$end]) {
                throw new Exception('起点和终点的位置ID错误');
            }
        } else {
            // 从下往上，起点排序<终点，起点ID<终点
            if ($location[$start] > $location[$end]) {
                throw new Exception('起点和终点的位置ID错误2');
            }
        }
        return $location;
    }

    /**
     * 根据排序规则，计算某条信息后的数量
     * @param string $table 表
     * @param int $event 1 从上往下 0 从下往上
     * @param int $start 起始位置
     * @param int $end 终点位置
     * @param string $order 排序
     * @param string $pk 表主键字段名
     * @return mixed
     * @throws DbException
     */
    protected function dragLimit(string $table, int $event, int $start, int $end, string $order = 'listorder desc,id desc', string $pk = 'id'): int
    {
        // 限定的数量，也就是实际要变更的数据量
        $subQuery = Db::table("{$table},(SELECT @rowno:=0) b")
            ->order($order)
            ->field("{$pk},(@rowno:=@rowno+1) as rowno")
            ->buildSql();
        return Db::table($subQuery . ' c')
            ->where($pk, $event ? $end : $start)
            ->field('rowno')
            ->value('rowno');
    }
}
