<?php
// +----------------------------------------------------------------------
// | PassDriverTraits
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\traits;

use think\facade\Request;

trait PassDriverTraits
{
    /**
     * 初始化驱动
     * @return string
     */
    protected function initDriver(): string
    {
        // 登录设备
        $deviceType = Request::param('devicetype', '', 'trim');
        return $this->convertDriver($deviceType);
    }

    /**
     * 转换设备类型成登录驱动
     * @param string $deviceType
     * @return string
     */
    protected function convertDriver(string $deviceType)
    {
        return match ($deviceType) {
            'ios_mp', 'android_mp' => 'QyappWx',
            'windows_mp', 'mac_mp', 'devtools_mp' => 'QyappWxpc',
            'h5', => 'QyappH5',
            'pc', => 'QyappPc',
            'ios_app', 'android_app', => 'QyappApp',
            default => 'Qyapp'
        };
    }
}
