<?php
// +----------------------------------------------------------------------
// | 当前登录用户
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\traits;

use app\model\user\User;
use pass\exception\AccessTokenException;
use pass\exception\UserDataException;
use pass\traits\TraitsAccessToken;
use think\facade\Request;

trait CurrentUserTraits
{
    use TraitsAccessToken;

    /**
     * 获取当前登录UID
     * @return int
     */
    protected function uid(): int
    {
        $uid = 0;
        if (Request::has('access_token')) {
            try {
                $accessTokenInfo = $this->getAceessTokenInfo(Request::param('access_token', ''));
                $uid = $accessTokenInfo['uid'];
            } catch (AccessTokenException $e) {
            }
        }
        return $uid;
    }

    /**
     * 获取当前用户信息
     * @return array
     */
    protected function userInfo(): array
    {
        $uid = $this->uid();
        $userInfo = [];
        if ($uid) {
            try {
                $user = User::getUserInfo($uid, null, 'uid');
                $data = $user->toArray();
                unset($data['password'], $data['id']);
                $userInfo = array_merge($userInfo, $data);
            } catch (UserDataException $e) {
            }
        }
        return $userInfo;
    }
}
