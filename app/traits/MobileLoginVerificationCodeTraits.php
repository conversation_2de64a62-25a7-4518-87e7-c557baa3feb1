<?php
// +----------------------------------------------------------------------
// | 手机号登录，验证码验证
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\traits;

use core\traits\ErrorClass;
use rpc\contract\upload\SmsInterface;
use think\facade\App;
use think\facade\Cache;
use think\facade\Env;

trait MobileLoginVerificationCodeTraits
{
    use ErrorClass;

    /**
     * 会员登录验证短信发送接口
     * @param string $phone 手机号
     * @param int|null $appointCode 指定验证码
     * @param bool $isSend 是否发送短信
     * @return bool
     */
    protected function verificationCodeSend(string $phone, int $appointCode = null, bool $isSend = true): bool
    {
        // 发短信每小时限额数
        $key2 = self::getverificationCodeKey($phone) . '/RepeatTimes';
        if (Cache::get($key2, 0) >= 5) {
            $this->error = '该手机号1小时内超过 5 次验证，请一小时后再试';
            return false;
        }
        // 获取六位数验证码
        $code = $appointCode ?: mt_rand(100000, 999999);
        $key = self::getverificationCodeKey($phone);

        // 短信发送结果
        $status = false;
        // 调用短信发送接口
        // 线上测试账号不发送短信
        if (!Env::get('app_debug', false) && $phone != '13800002023') {
            // 是否实际发送短信
            if ($isSend) {
                $smsInterface = App::make(SmsInterface::class);
                $result = $smsInterface->scenes($phone, 'Captcha', [$code, 'xckj']);
                // 验证发送结果，有一个成功表示发送成功了
                foreach ($result as $item) {
                    if ($item['status'] == 'success') {
                        $status = true;
                        break;
                    }
                }
            } else {
                $status = true;
            }
        } else {
            $status = true;
            $code = $appointCode ?: 123456;
        }

        if (true === $status) {
            // 记录到缓存已被验证用
            Cache::set($key, $code, 600);
            // 发送成功，每小时限额数+1
            $repeatTimes = Cache::get($key2, 0);
            if (empty($repeatTimes)) {
                Cache::set($key2, 1, 3600);
            } else {
                Cache::inc($key2, 1);
            }
            return true;
        }
        $this->error = '发送验证码失败';
        return false;
    }

    /**
     * 获取发送验证码的缓存key
     * @param string $mobile
     * @return string
     */
    public static function getverificationCodeKey(string $mobile): string
    {
        return "verification/sms/{$mobile}/pass_login_sms";
    }
}
