<?php
// +----------------------------------------------------------------------
// | CesuanServerTraits 测算-应用
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\traits\rpc;

use rpc\contract\cesuan\ServerInterface;
use think\facade\App;

trait CesuanServerTraits
{
    /**
     * 应用信息
     * @param int $serversId
     * @return array
     */
    protected function serverInfo(int $serversId): array
    {
        /**
         * @var ServerInterface $rpc
         */
        $rpc = App::make(ServerInterface::class);
        return $rpc->info($serversId);
    }
}
