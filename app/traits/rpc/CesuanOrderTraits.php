<?php
// +----------------------------------------------------------------------
// | CesuanOrderTraits 测算-订单
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\traits\rpc;

use rpc\contract\cesuan\OrderInterface;
use think\facade\App;

trait CesuanOrderTraits
{
    use CesuanServerTraits;

    /**
     * 订单信息
     * @param string $orderSn
     * @return array
     */
    protected function orderInfo(string $orderSn): array
    {
        /**
         * @var OrderInterface $rpc
         */
        $rpc = App::make(OrderInterface::class);
        return $rpc->info($orderSn);
    }
}
