<?php
// +----------------------------------------------------------------------
// | PaymentMethodTraits 支付方式
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\traits\rpc;

use rpc\contract\pay\PaymentMethodInterface;
use think\Exception;
use think\facade\App;
use think\facade\Log;

trait PaymentMethodTraits
{
    /**
     * 获取支付ID
     * @param string $payMethod 支付驱动标识
     * @return int
     * @throws Exception
     */
    protected function getPayMethodId(string $payMethod): int
    {
        /**
         * @var PaymentMethodInterface $paymentMethodInterface
         */
        $paymentMethodInterface = App::make(PaymentMethodInterface::class);
        $info = $paymentMethodInterface->paymentMethodInfo($payMethod);
        $payMethodId = $info['id'] ?? 0;
        if (empty($payMethodId)) {
            // 再次尝试下
            $info = $paymentMethodInterface->paymentMethodInfo($payMethod);
            $payMethodId = $info['id'] ?? 0;
            if (empty($payMethodId)) {
                Log::record(
                    [
                        'tips' => 'getPayMethodId',
                        'msg' => '该支付方式不存在',
                        '$payMethod' => $payMethod,
                        '$info' => $info,
                    ],
                    'error'
                );
                throw new Exception('该支付方式不存在');
            }
        }
        return $payMethodId;
    }
}
