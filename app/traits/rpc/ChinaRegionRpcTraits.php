<?php
// +----------------------------------------------------------------------
// | ChinaRegionRpcTraits 省市区
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\traits\rpc;

use rpc\contract\member\ChinaRegionInterface;
use think\facade\App;

trait ChinaRegionRpcTraits
{
    /**
     * 获取省份
     * @param string $province 省份
     * @return int
     */
    public function getProvinceId(string $province)
    {
        /**
         * @var ChinaRegionInterface $rpc
         */
        $rpc = App::make(ChinaRegionInterface::class);
        return $rpc->getProvinceId($province);
    }

    /**
     * 获取城市
     * @param string $city 城市
     * @param int|null $pid 父ID
     * @return int
     */
    public function getCityId(string $city, ?int $pid = null)
    {
        /**
         * @var ChinaRegionInterface $rpc
         */
        $rpc = App::make(ChinaRegionInterface::class);
        return $rpc->getCityId($city, $pid);
    }

    /**
     * 获取区/县
     * @param string $area 区/县
     * @param int|null $pid 父ID
     * @return int
     */
    public function getAreaId(string $area, ?int $pid = null)
    {
        /**
         * @var ChinaRegionInterface $rpc
         */
        $rpc = App::make(ChinaRegionInterface::class);
        return $rpc->getAreaId($area, $pid);
    }

    /**
     * 根据中文获得对应的id
     * @param string $name 地区名
     * @param int $pid 父id
     * @param int $level 层级
     * @return int
     */
    protected function cnNameId(string $name, int $pid, int $level = 1): int
    {
        /**
         * @var ChinaRegionInterface $rpc
         */
        $rpc = App::make(ChinaRegionInterface::class);
        return $rpc->cnNameId($name, $pid, $level);
    }

    /**
     * 返回Tree使用的数组
     * @param bool $all 是否返回全部
     * @return array
     */
    public function getRegionTreeArray(bool $all = true): array
    {
        /**
         * @var ChinaRegionInterface $rpc
         */
        $rpc = App::make(ChinaRegionInterface::class);
        return $rpc->getTreeArray($all);
    }

    /**
     * 获得地区详情
     * @param string $id 地区id
     * @return array
     */
    public function regionInfo(string $id): array
    {
        /**
         * @var ChinaRegionInterface $rpc
         */
        $rpc = App::make(ChinaRegionInterface::class);
        return $rpc->info($id);
    }

    /**
     * 获取下级数据
     * @param int $pid
     * @return array
     */
    public function regionChild(int $pid): array
    {
        /**
         * @var ChinaRegionInterface $rpc
         */
        $rpc = App::make(ChinaRegionInterface::class);
        return $rpc->child($pid);
    }

    /**
     * 提取地址的省市区
     * @param string $address
     * @return array
     */
    public function getRegion(string $address): array
    {
        /**
         * @var ChinaRegionInterface $rpc
         */
        $rpc = App::make(ChinaRegionInterface::class);
        return $rpc->getRegion($address);
    }
}
