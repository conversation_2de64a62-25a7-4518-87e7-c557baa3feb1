<?php
// +----------------------------------------------------------------------
// | 获取后台用户信息
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\traits;

use pass\objects\User;
use rpc\contract\nadm\UserInterface;
use think\facade\App;

trait GetAdminUserInfoTraits
{
    /**
     * 获取后台用户信息
     * @param int $uid
     * @return User
     */
    protected function getAdmUserInfo(int $uid): User
    {
        /**
         * @var $rpc UserInterface
         */
        $rpc = App::make(UserInterface::class);
        $user = $rpc->info($uid);
        return new User($user, 'id', 'username');
    }
}
