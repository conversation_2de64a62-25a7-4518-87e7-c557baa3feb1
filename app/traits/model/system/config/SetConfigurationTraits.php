<?php
// +----------------------------------------------------------------------
// | 配置设置规范化
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\traits\model\system\config;

use think\facade\Validate;
use think\helper\Str;

trait SetConfigurationTraits
{
    /**
     * 客服微信
     * @param array $data
     * @return bool
     */
    protected function setConfigCustomerWx($data)
    {
        if (!is_array($data)) {
            $this->error = '数据类型错误';
            return false;
        }
        $contact = [];
        foreach ($data as $item) {
            if (empty($item['wx']) || empty($item['qr']) || empty($item['phone'])) {
                $this->error = '微信号、二维码、手机号不能为空';
                return false;
            }
            if (!Validate::is($item['qr'], 'url')) {
                $this->error = '二维码地址有误';
                return false;
            }
            $contact[$item['wx']] = $item;
        }
        if (empty($contact)) {
            $this->error = '客服微信为空';
            return false;
        }
        $this->value = json_encode(array_values($contact));
        return true;
    }

    /**
     * H5域名
     * @param array $data
     * @return bool
     */
    protected function setConfigH5Domain($data)
    {
        if (!is_array($data)) {
            $this->error = '数据类型错误';
            return false;
        }
        $domain = [];
        foreach ($data as $do) {
            if (Str::startsWith($do, 'http') || Str::endsWith($do, '/') || Str::startsWith($do, ':')) {
                continue;
            }
            $domain[$do] = $do;
        }
        if (empty($domain)) {
            $this->error = '合法域名不能为空';
            return false;
        }
        $this->value = json_encode(array_values($domain));
        return true;
    }

    /**
     * 审核中版本
     * @param array $data
     * @return bool
     */
    protected function setConfigReviewVersion($data)
    {
        if (!is_array($data)) {
            $this->error = '数据类型错误';
            return false;
        }
        $domain = [];
        foreach ($data as $do) {
            $domain[$do] = trim($do);
        }
        $this->value = json_encode(array_values($domain));
        return true;
    }

    /**
     * 设置
     * @param string|array|int $value
     * @return bool
     */
    public function setConfig($value): bool
    {
        $method = "setConfig" . Str::studly($this->iden);
        if (method_exists($this, $method)) {
            if (true !== $this->{$method}($value)) {
                return false;
            }
        } else {
            if (is_array($value)) {
                $value = json_encode($value);
            }
            $this->value = $value;
        }
        return $this->save();
    }

    /**
     * 获取配置
     * @return array|false|int|mixed|string
     */
    public function config()
    {
        $method = "getConfig" . Str::studly($this->iden);
        if (method_exists($this, $method)) {
            $value = $this->{$method}();
        } else {
            $value = json_decode($this->value, true);
            if (empty($value)) {
                $value = $this->value;
            }
        }
        return $value;
    }
}
