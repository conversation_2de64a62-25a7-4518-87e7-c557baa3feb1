<?php
// +----------------------------------------------------------------------
// | BackgroundOperatorTraits 后台操作者
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\traits\model;

use pass\objects\User as passUser;

trait BackgroundOperatorTraits
{
    /**
     * 后台用户通行证
     * @var passUser|null
     */
    protected ?passUser $admin;

    /**
     * 后台用户UID
     * @var int
     */
    protected int $adminUid = 0;

    /**
     * 后台用户名
     * @var string
     */
    protected string $adminUsername = '';

    /**
     * 设置操作管理员
     * @param passUser $admin
     * @return $this
     */
    public function setOperator(passUser $admin)
    {
        $this->admin = $admin;
        $this->adminUid = $admin->uid();
        $this->adminUsername = $admin->username();
        return $this;
    }
}
