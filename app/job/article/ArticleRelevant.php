<?php
// +----------------------------------------------------------------------
// | ArticleRelevant 文章相关数据处理
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------
namespace app\job\article;

use app\model\article\Article;
use app\model\article\PositionData;
use app\model\article\TagsList;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\queue\Job;

class ArticleRelevant
{
    /**
     * @param Job $job
     * @param $data
     * @return void
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function fire(Job $job, $data)
    {
        $id = $data['id'] ?? 0;
        $info = Article::find($id);
        if (empty($info)) {
            $job->delete();
            return;
        }
        // 刷新推荐位
        PositionData::where('type', 1)
            ->where('original_id', $info->id)
            ->chunk(20, function ($list) use ($info) {
                /**
                 * @var PositionData[] $list
                 */
                foreach ($list as $pos) {
                    $pos->upData($info);
                }
            });
        // 刷新标签
        TagsList::where('aid', $info->id)
            ->chunk(20, function ($list) use ($info) {
                /**
                 * @var TagsList[] $list
                 */
                foreach ($list as $tag) {
                    $tag->title = $info->title;
                    $tag->thumb = $info->thumb;
                    $tag->description = $info->description;
                    $tag->save();
                }
            });
        $job->delete();
    }
}
