<?php
// +----------------------------------------------------------------------
// | Category 分类
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------
namespace app\job\article;

use app\model\article\Category as CategoryModel;
use Psr\SimpleCache\InvalidArgumentException;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\queue\Job;

class Category
{
    /**
     * @param Job $job
     * @param $data
     * @return void
     * @throws InvalidArgumentException
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function fire(Job $job, $data)
    {
        $id = $data['id'] ?? 0;
        $info = CategoryModel::find($id);
        if (empty($info)) {
            $job->delete();
            return;
        }
        // 刷新栏目关系
        CategoryModel::chunk(20, function ($list) {
            foreach ($list as $cat) {
                $cat->repair();
            }
        });
        $job->delete();
    }
}
