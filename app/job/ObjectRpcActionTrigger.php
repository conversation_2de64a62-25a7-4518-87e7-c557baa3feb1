<?php
// +----------------------------------------------------------------------
// | 调用RPC对象方法
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\job;

use Exception;
use think\facade\App;
use think\facade\Log;
use think\queue\Job;

class ObjectRpcActionTrigger
{
    /**
     * @param Job $job
     * @param array $data
     * @throws Exception
     */
    public function fire(Job $job, $data)
    {
        $rpc = $data['rpc'] ?? '';
        $action = $data['action'] ?? '';
        $vars = $data['vars'] ?? [];
        if (empty($rpc)) {
            $job->delete();
            return;
        }
        try {
            $rpc = App::make($rpc);
            // 有设置调用方法
            if (!empty($action) && is_object($rpc)) {
                if (!method_exists($rpc, $action)) {
                    $job->delete();
                    return;
                }
                App::invoke([$rpc, $action], $vars);
            }
            $job->delete();
        } catch (Exception $e) {
            Log::record(
                [
                    'tips' => 'ObjectRpcActionTrigger',
                    'message' => $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine(),
                    'data' => $data,
                ],
                'error'
            );
            throw $e;
        }
    }
}
