<?php
// +----------------------------------------------------------------------
// | 调用对象方法
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\job;

use ReflectionException;
use think\exception\FuncNotFoundException;
use think\facade\App;
use think\facade\Log;
use think\queue\Job;

class ObjectActionTrigger
{
    /**
     * 示例data数据用法
     * @return array[]
     */
    public function example()
    {
        return [
            // 初始化对象：类，静态方法(可空)，参数(静态方法为空的时候，给的是实例化对象参数)
            'object' => [static::class, 'example', [1]], // 静态方法调用
            //'object' => [static::class, 'example'], // 静态方法调用
            //'object' => [static::class','',[1]], // 实例化对象
            // 调用对象方法，支持多个，基于对象，不支持返回值传递
            'actions' => [
                // 用法1，带参数
                'example' => [1, 2, 3],
                // 用法2，不带参数
                'act',
            ],
        ];
    }

    /**
     * @param Job $job
     * @param array $data
     * @throws ReflectionException
     */
    public function fire(Job $job, array $data)
    {
        if (empty($data['object'][0])) {
            Log::record(
                [
                    'tips' => 'ObjectActionTrigger没有class',
                    '$data' => $data,
                ],
                'error'
            );
            $job->delete();
            return;
        }
        try {
            // 获取对象
            $object = self::getObject($data);
            // 判断是否对象
            if (!is_object($object)) {
                $job->delete();
                return;
            }
            // actions
            if (!empty($data['actions'])) {
                foreach ($data['actions'] as $act => $var) {
                    if (is_int($act)) {
                        $act = $var;
                        $var = [];
                    }
                    App::invoke([$object, $act], $var);
                }
            }
            $job->delete();
        } catch (\Exception $e) {
            Log::record(
                [
                    'tips' => 'ObjectActionTrigger',
                    'message' => $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine(),
                    'data' => $data,
                    'debug_backtrace' => debug_backtrace(2),
                ],
                'error'
            );
            throw $e;
        }
    }

    /**
     * 获取对象
     * @param array $data
     * @return object
     * @throws ReflectionException
     */
    public static function getObject(array $data)
    {
        $class = $data['object'][0];
        $act = $data['object'][1] ?? '';
        $vars = (array)($data['object'][2] ?? []);
        try {
            // 有act的时候，是走静态方法调用，例如 Object::act()
            if (!empty($act)) {
                // 不支持魔术方法之类的，比如模型的find方法
                $object = App::invokeMethod(implode('::', [$class, $act]), $vars);
            } else {
                // 实例化一个对象
                $object = App::invokeClass($class, $vars);
            }
        } catch (ReflectionException | FuncNotFoundException $e) {
            if (!class_exists($class)) {
                throw $e;
            }
            $object = $class::$act(...$vars);
        }
        return $object;
    }
}
