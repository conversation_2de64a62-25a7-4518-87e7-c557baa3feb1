<?php
// +----------------------------------------------------------------------
// | 配置
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\api\v1;

use api\ApiResult;
use app\model\system\Config as ConfigModel;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\facade\Request;
use think\model\Collection;

class Config
{
    /**
     * 获取全部配置
     * @return ApiResult
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function all()
    {
        $wz = Request::param('wz', '0', 'trim');
        /**
         * @var ConfigModel[]|Collection $list
         * @var ConfigModel $item
         */
        $list = ConfigModel::order('id', 'asc')->select();
        $config = [];
        foreach ($list->column(null, 'iden') as $iden => $item) {
            if ($wz) {
                $data = $item->toArray();
                unset($data['id'], $data['create_time'], $data['update_time'], $data['value']);
                $data['config'] = $item->config();
                $config[$iden] = $data;
            } else {
                $config[$iden] = $item->config();
            }
        }
        return ApiResult::success($config);
    }
}
