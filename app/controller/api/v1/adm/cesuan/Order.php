<?php
// +----------------------------------------------------------------------
// | 测算-订单管理
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\api\v1\adm\cesuan;

use api\admin\ApiBase;
use api\ApiResult;
use api\traits\controller\GeneralBaseCurdTraits;
use api\traits\controller\PageTraits;
use app\model\cesuan\Order as OrderModel;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;

class Order extends ApiBase
{
    use GeneralBaseCurdTraits;
    use PageTraits;

    /**
     * 权限映射
     * @var array
     */
    protected array $accessMap = [
        'public' => ['info'],
        'order/index' => ['list', 'delete'],
    ];

    /**
     * 列表
     * @return ApiResult
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function list()
    {
        $param = [
            // 状态
            'status' => $this->request->get('status', '', 'trim'),
            // 支付状态
            'pay_status' => $this->request->get('pay_status', '', 'trim'),
            // 订单编号
            'order_sn' => $this->request->get('order_sn', '', 'trim'),
            // 用户ID
            'uid' => $this->request->get('uid', '', 'trim'),
            // 服务ID
            'servers_id' => $this->request->get('servers_id', '', 'trim'),
        ];
        $query = OrderModel::order(['id' => 'desc']);
        $fun = function ($key, $value) use ($query) {
            switch ($key) {
                case 'status':
                case 'pay_status':
                case 'uid':
                case 'servers_id':
                    $query->where($key, $value);
                    break;
                case 'order_sn':
                    $query->where($key, 'like', "%{$value}%");
                    break;
            }
        };
        foreach ($param as $key => $value) {
            if ($value !== '') {
                $fun($key, $value);
            }
        }
        return $this->basePage($query, $this->getPageId(), null, null, $this->getLimit());
    }

    /**
     * 详情
     * @return ApiResult
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function info()
    {
        $id = $this->request->get('id', '', 'trim');
        if (empty($id)) {
            return ApiResult::error('订单ID或者编号不能为空');
        }
        $query = OrderModel::where('1=1');
        if (is_numeric($id)) {
            $query->where('id', $id);
        } else {
            $query->where('order_sn', $id);
        }
        $info = $query->find();
        if (empty($info)) {
            return ApiResult::error('订单不存在');
        }
        $info->append(['server_url']);
        return ApiResult::success($info->toArray());
    }

    /**
     * 删除（数据库软删除）
     * @return ApiResult
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function delete()
    {
        $id = $this->request->get('id', '', 'trim');
        if (empty($id)) {
            return ApiResult::error('订单ID或者编号不能为空');
        }
        $query = OrderModel::where('1=1');
        if (is_numeric($id)) {
            $query->where('id', $id);
        } else {
            $query->where('order_sn', $id);
        }
        $info = $query->find();
        if (empty($info)) {
            return ApiResult::error('订单不存在');
        }
        $info->delete();
        return ApiResult::success(true);
    }
}
