<?php
// +----------------------------------------------------------------------
// | Protocol 协议内容
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\api\v1\adm\tool;

use api\admin\ApiBase;
use api\ApiResult;
use api\traits\controller\GeneralBaseCurdTraits;
use api\traits\controller\PageTraits;
use app\model\tool\Protocol as ProtocolModel;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;

class Protocol extends ApiBase
{
    use GeneralBaseCurdTraits;
    use PageTraits;

    /**
     * 权限映射
     * @var array
     */
    protected array $accessMap = [
        'public' => ['info'],
        'protocol/list' => ['list'],
        'protocol/edit' => ['add', 'edit', 'delete'],
    ];

    /**
     * 列表
     * @return ApiResult
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function list()
    {
        $param = [
            'title' => $this->request->get('title', '', 'trim'),
            'at' => $this->request->get('at', '', 'trim'),
        ];
        $query = ProtocolModel::order(['id' => 'desc']);
        $fun = function ($key, $value) use ($query) {
            switch ($key) {
                case 'title':
                    $query->whereLike($key, "%{$value}%");
                    break;
                case 'at':
                    $query->where($key, $value);
                    break;
            }
        };
        foreach ($param as $key => $value) {
            if ($value != '') {
                $fun($key, $value);
            }
        }
        return $this->basePage($query, $this->getPageId(), null, null, $this->getLimit());
    }

    /**
     * 详情
     * @return ApiResult
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function info()
    {
        $id = $this->request->get('id', '', 'trim');
        $info = ProtocolModel::where('id', $id)->find();
        if (empty($info)) {
            return ApiResult::error('数据不存在');
        }
        return ApiResult::success($info->toArray());
    }

    /**
     * 添加
     * @return ApiResult
     */
    public function add()
    {
        $data = [
            'title' => $this->request->post('title', '', 'trim'),
            'at' => $this->request->post('at', '', 'trim'),
            'version_str' => $this->request->post('version_str', '', 'trim'),
            'content' => $this->request->post('content', '', 'trim'),
            'implement_time' => $this->request->post('implement_time', '', 'trim'),
        ];
        $model = new ProtocolModel();
        if ($model->plus($data)) {
            return ApiResult::success(true);
        }
        return ApiResult::error($model->getError() ?: '添加失败');
    }

    /**
     * 编辑
     * @return ApiResult
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function edit()
    {
        $id = $this->request->post('id', 0, 'intval');
        /**
         * @var ProtocolModel $info
         */
        $info = ProtocolModel::where('id', $id)->find();
        if (empty($info)) {
            return ApiResult::error('数据不存在');
        }
        $data = [
            'title' => $this->request->post('title', null, 'trim'),
            'at' => $this->request->post('at', null, 'trim'),
            'version_str' => $this->request->post('version_str', null, 'trim'),
            'content' => $this->request->post('content', null, 'trim'),
            'implement_time' => $this->request->post('implement_time', null, 'trim'),
        ];
        // 过滤掉值为 null 的元素
        $data = array_filter($data, function ($value) {
            return $value !== null;
        });
        if ($info->edit($data)) {
            return ApiResult::success(true);
        }
        return ApiResult::error($info->getError() ?: '编辑失败');
    }

    /**
     * 删除
     * @return bool
     */
    public function delete()
    {
        $id = $this->request->get('id', 0, 'intval');
        return $this->baseDelete(ProtocolModel::class, $id);
    }
}
