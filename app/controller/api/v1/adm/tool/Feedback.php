<?php
// +----------------------------------------------------------------------
// | Feedback
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\api\v1\adm\tool;

use api\admin\ApiBase;
use api\ApiResult;
use api\traits\controller\GeneralBaseCurdTraits;
use api\traits\controller\PageTraits;
use app\model\tool\Feedback as FeedbackModel;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;

class Feedback extends ApiBase
{
    use GeneralBaseCurdTraits;
    use PageTraits;

    /**
     * 权限映射
     * @var array
     */
    protected array $accessMap = [
        'public' => ['info'],
        'feedback/list' => ['list', 'process'],
    ];

    /**
     * 列表
     * @return ApiResult
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function list()
    {
        $param = [
            // 状态
            'status' => $this->request->get('status', '', 'intval'),
        ];
        $query = FeedbackModel::order(['id' => 'desc']);
        $fun = function ($key, $value) use ($query) {
            switch ($key) {
                case 'status':
                    $query->where($key, $value);
                    break;
            }
        };
        foreach ($param as $key => $value) {
            if ($value != '') {
                $fun($key, $value);
            }
        }
        return $this->basePage($query, $this->getPageId(), null, null, $this->getLimit());
    }

    /**
     * 标记处理
     * @return ApiResult
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function process()
    {
        $id = $this->request->get('id', 0, 'intval');
        /**
         * @var FeedbackModel $info
         */
        $info = FeedbackModel::where('id', $id)->find();
        if (empty($info)) {
            return ApiResult::error('数据不存在');
        }
        if ($info->setOperator($this->user)->process()) {
            return ApiResult::success(true);
        }
        return ApiResult::error($info->getError() ?: '编辑失败');
    }
}
