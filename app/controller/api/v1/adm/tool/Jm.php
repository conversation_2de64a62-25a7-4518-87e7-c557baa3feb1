<?php
// +----------------------------------------------------------------------
// | 解梦管理
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\api\v1\adm\tool;

use api\admin\ApiBase;
use api\ApiResult;
use api\traits\controller\GeneralBaseCurdTraits;
use api\traits\controller\PageTraits;
use app\model\tool\Jm as JmModel;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\facade\Validate;

class Jm extends ApiBase
{
    use GeneralBaseCurdTraits;
    use PageTraits;

    /**
     * 权限映射
     * @var array
     */
    protected array $accessMap = [
        'public' => ['info', 'category'],
        'jm/list' => ['list'],
        'jm/add' => ['add'],
        'jm/edit' => ['edit'],
        'jm/delete' => ['delete'],
    ];

    /**
     * 解梦栏目分类
     * @return ApiResult
     */
    public function category()
    {
        return ApiResult::success(JmModel::$categoryList);
    }

    /**
     * 详情
     * @return ApiResult
     */
    public function info()
    {
        $id = $this->request->get('id', '', 'trim');
        if (empty($id)) {
            return ApiResult::error('ID不能为空');
        }
        return $this->baseInfo(JmModel::class, $id);
    }

    /**
     * 列表
     * @return ApiResult
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function list()
    {
        $param = [
            // 搜索
            'keyword' => $this->request->get('keyword', '', 'trim'),
            // 状态
            'status' => $this->request->get('status', '', 'trim'),
            // 分类
            'category' => $this->request->get('category', '', 'trim'),
            // 时间范围搜索
            'time_type' => $this->request->get('time_type', '', 'trim'),
            'start_time' => $this->request->get('start_time', '', 'trim'),
            'end_time' => $this->request->get('end_time', '', 'trim'),
        ];
        $query = JmModel::order(['id' => 'desc'])->withoutField(['content']);
        $fun = function ($key, $value) use ($query, $param) {
            switch ($key) {
                case 'status':
                case 'category':
                    $query->where($key, $value);
                    break;
                case 'keyword':
                    if (Validate::is($value, 'integer')) {
                        $query->where('id', $value);
                    } else {
                        $keywords = explode(' ', $value);
                        if (count($keywords) > 1) {
                            $likes = [];
                            foreach ($keywords as $k) {
                                $likes[] = "%{$k}%";
                            }
                            $query->whereLike('title', $likes);
                        } else {
                            $query->whereLike('title', "%{$value}%");
                        }
                    }
                    break;
                case 'start_time':
                case 'end_time':
                    $field = match ((int)$param['time_type']) {
                        1 => 'create_time',
                        2 => 'update_time',
                        default => 'release_time',
                    };
                    $time = strtotime($value);
                    if ($key == 'start_time') {
                        $query->where($field, '>=', strtotime(date('Y-m-d', $time)));
                    } else {
                        $query->where($field, '<=', strtotime(date('Y-m-d 23:59:59', $time)));
                    }
                    break;
            }
        };
        foreach ($param as $key => $value) {
            if ($value !== '') {
                $fun($key, $value);
            }
        }
        return $this->basePage($query, $this->getPageId(), null, null, $this->getLimit());
    }

    /**
     * 添加
     * @return ApiResult
     */
    public function add()
    {
        $data = [
            'title' => $this->request->post('title', '', 'trim'),
            'category' => $this->request->post('category', 0, 'intval'),
            'status' => $this->request->post('status', 0, 'intval'),
            'keyword' => $this->request->post('keyword', '', 'trim'),
            'thumb' => $this->request->post('thumb', '', 'trim'),
            'author' => $this->request->post('author', '乾元日历', 'trim'),
            'source' => $this->request->post('source', '互联网', 'trim'),
            'description' => $this->request->post('description', '', 'trim'),
            'hits' => $this->request->post('hits', 0, 'intval'),
            'release_time' => $this->request->post('release_time', time(), 'trim'),
            'content' => $this->request->post('content', '', 'trim'),
        ];
        $model = new JmModel();
        if ($model->setOperator($this->user)->plus($data)) {
            return ApiResult::success(true);
        }
        return ApiResult::error($model->getError() ?: '添加失败');
    }

    /**
     * 编辑
     * @return ApiResult
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function edit()
    {
        $id = $this->request->post('id', 0, 'intval');
        /**
         * @var JmModel $info
         */
        $info = JmModel::where('id', $id)->find();
        if (empty($info)) {
            return ApiResult::error('数据不存在');
        }
        $data = [
            'title' => $this->request->post('title', null, 'trim'),
            'category' => $this->request->post('category', null, 'intval'),
            'status' => $this->request->post('status', null, 'intval'),
            'keyword' => $this->request->post('keyword', null, 'trim'),
            'thumb' => $this->request->post('thumb', null, 'trim'),
            'author' => $this->request->post('author', null, 'trim'),
            'source' => $this->request->post('source', null, 'trim'),
            'description' => $this->request->post('description', null, 'trim'),
            'hits' => $this->request->post('hits', null, 'intval'),
            'release_time' => $this->request->post('release_time', null, 'trim'),
            'content' => $this->request->post('content', null, 'trim'),
        ];
        // 过滤掉值为 null 的元素
        $data = array_filter($data, function ($value) {
            return $value !== null;
        });
        if ($info->setOperator($this->user)->edit($data)) {
            return ApiResult::success(true);
        }
        return ApiResult::error($info->getError() ?: '编辑失败');
    }

    /**
     * 删除（数据库软删除）
     * @return bool|ApiResult
     */
    public function delete()
    {
        $id = $this->request->get('id', '', 'trim');
        if (empty($id)) {
            return ApiResult::error('ID不能为空');
        }
        return $this->baseDelete(JmModel::class, $id);
    }
}
