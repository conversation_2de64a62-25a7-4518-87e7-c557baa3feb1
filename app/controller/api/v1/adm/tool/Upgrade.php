<?php
// +----------------------------------------------------------------------
// | Upgrade 版本发布
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\api\v1\adm\tool;

use api\admin\ApiBase;
use api\ApiResult;
use api\traits\controller\GeneralBaseCurdTraits;
use api\traits\controller\PageTraits;
use app\model\tool\Upgrade as UpgradeModel;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;

class Upgrade extends ApiBase
{
    use GeneralBaseCurdTraits;
    use PageTraits;

    /**
     * 权限映射
     * @var array
     */
    protected array $accessMap = [
        'upgrade/index' => ['list', 'info', 'add', 'edit', 'delete'],
    ];

    /**
     * 列表
     * @return ApiResult
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function list()
    {
        $param = [
            // 状态
            'status' => $this->request->get('status', '', 'intval'),
            'platform' => $this->request->get('status', '', 'trim'),
        ];
        $query = UpgradeModel::order(['id' => 'desc']);
        $fun = function ($key, $value) use ($query) {
            switch ($key) {
                case 'status':
                    $query->where($key, $value);
                    break;
                case 'platform':
                    $query->whereFindInSet('platform', $value);
                    break;
            }
        };
        foreach ($param as $key => $value) {
            if ($value != '') {
                $fun($key, $value);
            }
        }
        return $this->basePage($query, $this->getPageId(), null, null, $this->getLimit());
    }

    /**
     * 详情
     * @return ApiResult
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function info()
    {
        $id = $this->request->get('id', '', 'trim');
        $info = UpgradeModel::where('id', $id)->find();
        if (empty($info)) {
            return ApiResult::error('数据不存在');
        }
        return ApiResult::success($info->toArray());
    }

    /**
     * 添加
     * @return ApiResult
     */
    public function add()
    {
        $data = [
            'title' => $this->request->post('title', '', 'trim'),
            'status' => $this->request->post('status', '0', 'trim'),
            'update_method' => $this->request->post('update_method', '1', 'trim'),
            'update_force' => $this->request->post('update_force', '0', 'trim'),
            'update_content' => $this->request->post('update_content', '', 'trim'),
            'platform' => $this->request->post('platform', '1', 'trim'),
            'version' => $this->request->post('version_str', '', 'trim'),
            'min_version' => $this->request->post('min_version', '', 'trim'),
            'url' => $this->request->post('url', '', 'trim'),
        ];
        $model = new UpgradeModel();
        if ($model->plus($data)) {
            return ApiResult::success(true);
        }
        return ApiResult::error($model->getError() ?: '添加失败');
    }

    /**
     * 编辑
     * @return ApiResult
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function edit()
    {
        $id = $this->request->post('id', 0, 'intval');
        /**
         * @var UpgradeModel $info
         */
        $info = UpgradeModel::where('id', $id)->find();
        if (empty($info)) {
            return ApiResult::error('数据不存在');
        }
        $data = [
            'title' => $this->request->post('title', null, 'trim'),
            'status' => $this->request->post('status', null, 'trim'),
            'update_method' => $this->request->post('update_method', null, 'trim'),
            'update_force' => $this->request->post('update_force', null, 'trim'),
            'update_content' => $this->request->post('update_content', null, 'trim'),
            'platform' => $this->request->post('platform', null, 'trim'),
            'version' => $this->request->post('version_str', null, 'trim'),
            'min_version' => $this->request->post('min_version', null, 'trim'),
            'url' => $this->request->post('url', null, 'trim'),
        ];
        // 过滤掉值为 null 的元素
        $data = array_filter($data, function ($value) {
            return $value !== null;
        });
        if ($info->edit($data)) {
            return ApiResult::success(true);
        }
        return ApiResult::error($info->getError() ?: '编辑失败');
    }

    /**
     * 删除
     * @return bool
     */
    public function delete()
    {
        $id = $this->request->get('id', 0, 'intval');
        return $this->baseDelete(UpgradeModel::class, $id);
    }
}
