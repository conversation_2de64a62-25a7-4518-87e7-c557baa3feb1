<?php
// +----------------------------------------------------------------------
// | Config
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\api\v1\adm\tool;

use api\admin\ApiBase;
use api\ApiResult;
use app\model\system\Config as ConfigModel;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\Exception;

class Config extends ApiBase
{
    /**
     * 权限映射
     * @var array
     */
    protected array $accessMap = [
        'config/index' => ['save'],
    ];

    /**
     * 保存配置
     * @return ApiResult
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function save()
    {
        $param = $this->request->post();
        unset($param['access_token'], $param['app_id'], $param['request_time'], $param['sign2'], $param['version'], $param['controller'], $param['action']);
        $list = ConfigModel::whereIn('iden', array_keys($param))->select();
        if ($list->isEmpty()) {
            return ApiResult::error('该配置不存在');
        }
        $idenList = $list->column(null, 'iden');
        try {
            foreach ($param as $iden => $value) {
                if (!isset($idenList[$iden])) {
                    continue;
                }
                /**
                 * @var ConfigModel $config
                 */
                $config = $idenList[$iden];
                $value = json_decode($value, true);
                if (!empty(json_last_error())) {
                    throw new Exception('数据格式错误：' . json_last_error_msg());
                }
                if (true !== $config->setConfig($value)) {
                    throw new Exception($config->getError() ?: '保存出错');
                }
            }
            return ApiResult::success($list);
        } catch (\Throwable | \Exception $e) {
            return ApiResult::error($e->getMessage());
        }
    }
}
