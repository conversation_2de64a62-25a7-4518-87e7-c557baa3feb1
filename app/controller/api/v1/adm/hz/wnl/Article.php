<?php
// +----------------------------------------------------------------------
// | 万年历-文章管理
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\api\v1\adm\hz\wnl;

use api\admin\ApiBase;
use api\ApiResult;
use api\traits\controller\GeneralBaseCurdTraits;
use api\traits\controller\PageTraits;
use app\model\hz\wnl\Article as ArticleModel;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;

class Article extends ApiBase
{
    use GeneralBaseCurdTraits;
    use PageTraits;

    /**
     * 权限映射
     * @var array
     */
    protected array $accessMap = [
        'public' => ['info'],
        'hz.wnl.article/list' => ['list'],
        'hz.wnl.article/add' => ['add'],
        'hz.wnl.article/edit' => ['edit'],
        'hz.wnl.article/delete' => ['delete'],
    ];

    /**
     * 详情
     * @return ApiResult
     */
    public function info()
    {
        $id = $this->request->get('id', '', 'trim');
        if (empty($id)) {
            return ApiResult::error('ID不能为空');
        }
        return $this->baseInfo(ArticleModel::class, $id);
    }

    /**
     * 列表
     * @return ApiResult
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function list()
    {
        $param = [
            // 状态
            'status' => $this->request->get('status', '', 'trim'),
            // 标题
            'title' => $this->request->get('title', '', 'trim'),
            // 类别
            'category' => $this->request->get('category', '', 'trim'),
        ];
        $query = ArticleModel::order(['id' => 'desc']);
        $fun = function ($key, $value) use ($query) {
            switch ($key) {
                case 'status':
                case 'category':
                    $query->where($key, $value);
                    break;
                case 'title':
                    $query->whereLike($key, "%{$value}%");
                    break;
            }
        };
        foreach ($param as $key => $value) {
            if ($value !== '') {
                $fun($key, $value);
            }
        }
        return $this->basePage($query, $this->getPageId(), null, null, $this->getLimit());
    }

    /**
     * 添加
     * @return ApiResult
     */
    public function add()
    {
        $data = [
            'category' => $this->request->post('category', '', 'trim'),
            'title' => $this->request->post('title', '', 'trim'),
            'status' => $this->request->post('status', '0', 'trim'),
            'coverphoto' => $this->request->post('coverphoto', '', 'trim'),
            'author' => $this->request->post('author', '', 'trim'),
            'source' => $this->request->post('source', '乾元日历', 'trim'),
            'content' => $this->request->post('content', '', 'trim'),
            'release_time' => $this->request->post('release_time', '', 'trim'),
        ];
        $model = new ArticleModel();
        if ($model->plus($data)) {
            return ApiResult::success(true);
        }
        return ApiResult::error($model->getError() ?: '添加失败');
    }

    /**
     * 编辑
     * @return ApiResult
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function edit()
    {
        $id = $this->request->post('id', 0, 'intval');
        /**
         * @var ArticleModel $info
         */
        $info = ArticleModel::where('id', $id)->find();
        if (empty($info)) {
            return ApiResult::error('数据不存在');
        }
        $data = [
            'category' => $this->request->post('category', null, 'trim'),
            'title' => $this->request->post('title', null, 'trim'),
            'status' => $this->request->post('status', null, 'trim'),
            'coverphoto' => $this->request->post('coverphoto', null, 'trim'),
            'author' => $this->request->post('author', null, 'trim'),
            'source' => $this->request->post('source', null, 'trim'),
            'content' => $this->request->post('content', null, 'trim'),
            'release_time' => $this->request->post('release_time', null, 'trim'),
        ];
        // 过滤掉值为 null 的元素
        $data = array_filter($data, function ($value) {
            return $value !== null;
        });
        if ($info->edit($data)) {
            return ApiResult::success(true);
        }
        return ApiResult::error($info->getError() ?: '编辑失败');
    }

    /**
     * 删除（数据库软删除）
     * @return bool|ApiResult
     */
    public function delete()
    {
        $id = $this->request->get('id', '', 'trim');
        if (empty($id)) {
            return ApiResult::error('ID不能为空');
        }
        return $this->baseDelete(ArticleModel::class, $id);
    }
}
