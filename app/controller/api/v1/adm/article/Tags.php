<?php
// +----------------------------------------------------------------------
// | tag管理
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\api\v1\adm\article;

use api\admin\ApiBase;
use api\ApiResult;
use api\traits\controller\GeneralBaseCurdTraits;
use api\traits\controller\PageTraits;
use app\model\article\Category;
use app\model\article\Tags as TagsModel;
use app\model\article\TagsList;
use Psr\SimpleCache\InvalidArgumentException;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\facade\Validate;

class Tags extends ApiBase
{
    use GeneralBaseCurdTraits;
    use PageTraits;

    /**
     * 权限映射
     * @var array
     */
    protected array $accessMap = [
        'public' => ['info', 'tagInfo'],
        'tags/list' => ['tags', 'list', 'display'],
    ];

    /**
     * 标签详情
     * @return ApiResult
     */
    public function info()
    {
        $id = $this->request->get('id', '', 'trim');
        if (empty($id)) {
            return ApiResult::error('ID不能为空');
        }
        return $this->baseInfo(TagsModel::class, $id);
    }

    /**
     * 标签数据详情
     * @return ApiResult
     */
    public function tagInfo()
    {
        $id = $this->request->get('id', '', 'trim');
        if (empty($id)) {
            return ApiResult::error('ID不能为空');
        }
        return $this->baseInfo(TagsList::class, $id);
    }

    /**
     * tag列表
     * @return ApiResult
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function tags()
    {
        $query = TagsModel::order(['id' => 'desc']);
        $param = [
            // 搜索
            'keyword' => $this->request->get('keyword', '', 'trim'),
        ];
        $fun = function ($key, $value) use ($query, $param) {
            switch ($key) {
                case 'keyword':
                    if (Validate::is($value, 'integer')) {
                        $query->where('id', $value);
                    } else {
                        $keywords = explode(' ', $value);
                        if (count($keywords) > 1) {
                            $likes = [];
                            foreach ($keywords as $k) {
                                $likes[] = "%{$k}%";
                            }
                            $query->whereLike('name', $likes);
                        } else {
                            $query->whereLike('name', "%{$value}%");
                        }
                    }
                    break;
            }
        };
        foreach ($param as $key => $value) {
            if ($value !== '') {
                $fun($key, $value);
            }
        }
        return $this->basePage($query, $this->getPageId(), null, null, $this->getLimit(100));
    }

    /**
     * 列表
     * 标签信息列表
     * @return ApiResult
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * @throws InvalidArgumentException
     */
    public function list()
    {
        $param = [
            // 搜索
            'keyword' => $this->request->get('keyword', '', 'trim'),
            // tag
            'tag' => $this->request->get('tag', '', 'trim'),
            // 分类
            'catid' => $this->request->get('catid', '', 'trim'),
        ];
        $query = TagsList::with(['tag'])->order(['id' => 'desc']);
        $fun = function ($key, $value) use ($query, $param) {
            switch ($key) {
                case 'catid':
                    $cate = Category::info($value);
                    if (empty($cate)) {
                        $query->where('catid', $value);
                    } else {
                        $childs = $cate->arrchildid;
                        $query->whereIn('catid', $childs);
                    }
                    break;
                case 'tag':
                    $query->where('tag_name', $value);
                    break;
                case 'keyword':
                    if (Validate::is($value, 'integer')) {
                        $query->where('aid', $value);
                    } else {
                        $keywords = explode(' ', $value);
                        if (count($keywords) > 1) {
                            $likes = [];
                            foreach ($keywords as $k) {
                                $likes[] = "%{$k}%";
                            }
                            $query->whereLike('title', $likes);
                        } else {
                            $query->whereLike('title', "%{$value}%");
                        }
                    }
                    break;
            }
        };
        foreach ($param as $key => $value) {
            if ($value !== '') {
                $fun($key, $value);
            }
        }
        return $this->basePage($query, $this->getPageId(), null, null, $this->getLimit());
    }

    /**
     * 隐藏或者显示
     * @return ApiResult
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function display()
    {
        $id = $this->request->get('id', 0, 'intval');
        $display = $this->request->get('display', null, 'intval');
        /**
         * @var TagsModel $info
         */
        $info = TagsModel::where('id', $id)->find();
        if (empty($info)) {
            return ApiResult::error('数据不存在');
        }

        if ($info->display($display)) {
            return ApiResult::success(true);
        }
        return ApiResult::error($info->getError() ?: '操作失败');
    }
}
