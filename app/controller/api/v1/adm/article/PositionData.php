<?php
// +----------------------------------------------------------------------
// | PositionData推荐位数据
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\api\v1\adm\article;

use api\admin\ApiBase;
use api\ApiResult;
use api\traits\controller\GeneralBaseCurdTraits;
use api\traits\controller\PageTraits;
use app\model\article\PositionData as PositionDataModel;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\db\Query;
use think\facade\Validate;

class PositionData extends ApiBase
{
    use GeneralBaseCurdTraits;
    use PageTraits;

    /**
     * 权限映射
     * @var array
     */
    protected array $accessMap = [
        'public' => ['info'],
        'position_data/list' => ['list'],
        'position_data/add' => ['add', 'edit'],
        'position_data/delete' => ['delete'],
    ];

    /**
     * 详情
     * @return ApiResult
     */
    public function info()
    {
        $id = $this->request->get('id', '', 'trim');
        if (empty($id)) {
            return ApiResult::error('ID不能为空');
        }
        return $this->baseInfo(PositionDataModel::class, $id);
    }

    /**
     * 列表
     * @return ApiResult
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function list()
    {
        $param = [
            // 搜索
            'keyword' => $this->request->get('keyword', '', 'trim'),
            // 推荐位ID
            'posid' => $this->request->get('posid', '', 'trim'),
            // 类型
            'type' => $this->request->get('type', '', 'trim'),
            // 是否有缩略图
            'is_thumb' => $this->request->get('is_thumb', '', 'trim'),
            // 是否同步编辑
            'synedit' => $this->request->get('synedit', '', 'trim'),
            // 原始信息ID搜索
            'original_keyword' => $this->request->get('original_keyword', '', 'trim'),
        ];
        $query = PositionDataModel::with(['position'])->order(['id' => 'desc']);
        $fun = function ($key, $value) use ($query, $param) {
            switch ($key) {
                case 'posid':
                case 'type':
                case 'is_thumb':
                case 'synedit':
                    $query->where($key, $value);
                    break;
                case 'original_keyword':
                    $query->where(function (Query $query) use ($value) {
                        $query->where('original_id', $value)->whereOr('original_catid', $value);
                    });
                    break;
                case 'keyword':
                    if (Validate::is($value, 'integer')) {
                        $query->where('id', $value);
                    } else {
                        $keywords = explode(' ', $value);
                        if (count($keywords) > 1) {
                            $likes = [];
                            foreach ($keywords as $k) {
                                $likes[] = "%{$k}%";
                            }
                            $query->whereLike('title', $likes);
                        } else {
                            $query->whereLike('title', "%{$value}%");
                        }
                    }
                    break;
            }
        };
        foreach ($param as $key => $value) {
            if ($value !== '') {
                $fun($key, $value);
            }
        }
        return $this->basePage($query, $this->getPageId(), null, null, $this->getLimit());
    }

    /**
     * 编辑
     * @return ApiResult
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function edit()
    {
        $id = $this->request->post('id', 0, 'intval');
        /**
         * @var PositionDataModel $info
         */
        $info = PositionDataModel::where('id', $id)->find();
        if (empty($info)) {
            return ApiResult::error('数据不存在');
        }
        $data = [
            'title' => $this->request->post('title', null, 'trim'),
            'thumb' => $this->request->post('thumb', null, 'trim'),
            'data_json' => $this->request->post('data_json', null, 'trim'),
            'synedit' => $this->request->post('synedit', null, 'intval'),
            'listorder' => $this->request->post('listorder', null, 'intval'),
        ];
        // 过滤掉值为 null 的元素
        $data = array_filter($data, function ($value) {
            return $value !== null;
        });
        if (!empty($data['data_json'])) {
            $data['data'] = json_decode($data['data_json'], true);
        }
        if ($info->edit($data)) {
            return ApiResult::success(true);
        }
        return ApiResult::error($info->getError() ?: '编辑失败');
    }

    /**
     * 删除
     * @return bool|ApiResult
     */
    public function delete()
    {
        $id = $this->request->get('id', '', 'trim');
        if (empty($id)) {
            return ApiResult::error('ID不能为空');
        }
        return $this->baseDelete(PositionDataModel::class, $id);
    }
}
