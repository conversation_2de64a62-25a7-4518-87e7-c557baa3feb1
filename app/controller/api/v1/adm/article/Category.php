<?php
// +----------------------------------------------------------------------
// | 分类管理
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\api\v1\adm\article;

use api\admin\ApiBase;
use api\ApiResult;
use api\traits\controller\GeneralBaseCurdTraits;
use api\traits\controller\PageTraits;
use app\model\article\Category as CategoryModel;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\Exception;

class Category extends ApiBase
{
    use GeneralBaseCurdTraits;
    use PageTraits;

    /**
     * 权限映射
     * @var array
     */
    protected array $accessMap = [
        'public' => ['info', 'repair', 'tree'],
        'category/list' => ['list'],
        'category/add' => ['add', 'edit'],
        'category/delete' => ['delete'],
    ];

    /**
     * 详情
     * @return ApiResult
     */
    public function info()
    {
        $id = $this->request->get('id', '', 'trim');
        if (empty($id)) {
            return ApiResult::error('ID不能为空');
        }
        return $this->baseInfo(CategoryModel::class, $id);
    }

    /**
     * 修复栏目数据
     * 添加编辑栏目后执行下
     * @return ApiResult
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * @throws Exception
     */
    public function repair()
    {
        $id = $this->request->get('id', 0, 'intval');
        if ($id) {
            /**
             * @var CategoryModel $info
             */
            $info = CategoryModel::where('id', $id)->find();
            if (empty($info)) {
                return ApiResult::error('数据不存在');
            }
            return ApiResult::success($info->repair());
        } else {
            CategoryModel::chunk(20, function ($list) {
                foreach ($list as $cat) {
                    $cat->repair();
                }
            });
            return ApiResult::success(true);
        }
    }

    /**
     * tree树
     * @return ApiResult
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function tree()
    {
        $list = CategoryModel::order(['listorder' => 'desc', 'id' => 'desc'])
            ->field('id,name,status,parentid,child')
            ->select();
        return ApiResult::success($list);
    }

    /**
     * 列表
     * @return ApiResult
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function list()
    {
        $list = CategoryModel::order(['listorder' => 'desc', 'id' => 'desc'])
            ->select();
        return ApiResult::success($list);
    }

    /**
     * 添加
     * @return ApiResult
     */
    public function add()
    {
        $data = [
            'name' => $this->request->post('name', '', 'trim'),
            'parentid' => $this->request->post('parentid', '', 'trim'),
            'status' => $this->request->post('status', 0, 'intval'),
            'listorder' => $this->request->post('listorder', 0, 'intval'),
            'seo_keywords' => $this->request->post('seo_keywords', '', 'trim'),
            'seo_description' => $this->request->post('seo_description', '', 'trim'),
        ];
        $model = new CategoryModel();
        if ($model->plus($data)) {
            return ApiResult::success(true);
        }
        return ApiResult::error($model->getError() ?: '添加失败');
    }

    /**
     * 编辑
     * @return ApiResult
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function edit()
    {
        $id = $this->request->post('id', 0, 'intval');
        /**
         * @var CategoryModel $info
         */
        $info = CategoryModel::where('id', $id)->find();
        if (empty($info)) {
            return ApiResult::error('数据不存在');
        }
        $data = [
            'title' => $this->request->post('title', null, 'trim'),
            'parentid' => $this->request->post('parentid', null, 'trim'),
            'status' => $this->request->post('status', null, 'intval'),
            'listorder' => $this->request->post('listorder', null, 'intval'),
            'seo_keywords' => $this->request->post('seo_keywords', null, 'trim'),
            'seo_description' => $this->request->post('seo_description', null, 'trim'),
        ];
        // 过滤掉值为 null 的元素
        $data = array_filter($data, function ($value) {
            return $value !== null;
        });
        if ($info->edit($data)) {
            return ApiResult::success(true);
        }
        return ApiResult::error($info->getError() ?: '编辑失败');
    }

    /**
     * 删除
     * @return bool|ApiResult
     */
    public function delete()
    {
        $id = $this->request->get('id', 0, 'intval');
        if (empty($id)) {
            return ApiResult::error('ID不能为空');
        }
        return $this->baseDelete(CategoryModel::class, $id);
    }

}
