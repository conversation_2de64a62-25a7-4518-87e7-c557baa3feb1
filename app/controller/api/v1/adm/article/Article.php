<?php
// +----------------------------------------------------------------------
// | 文章管理
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\api\v1\adm\article;

use api\admin\ApiBase;
use api\ApiResult;
use api\traits\controller\GeneralBaseCurdTraits;
use api\traits\controller\PageTraits;
use app\model\article\Article as ArticleModel;
use app\model\article\Category;
use Psr\SimpleCache\InvalidArgumentException;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\facade\Queue;
use think\facade\Validate;

class Article extends ApiBase
{
    use GeneralBaseCurdTraits;
    use PageTraits;

    /**
     * 权限映射
     * @var array
     */
    protected array $accessMap = [
        'public' => ['info'],
        'article/list' => ['list'],
        'article/add' => ['add', 'edit'],
        'article/delete' => ['delete', 'restore'],
    ];

    /**
     * 详情
     * @return ApiResult
     */
    public function info()
    {
        $id = $this->request->get('id', '', 'trim');
        $isBody = $this->request->get('is_body', 0, 'intval');
        if (empty($id)) {
            return ApiResult::error('ID不能为空');
        }
        return $this->baseInfo($isBody ? ArticleModel::with(['content']) : ArticleModel::class, $id);
    }

    /**
     * 列表
     * @return ApiResult
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * @throws InvalidArgumentException
     */
    public function list()
    {
        $param = [
            // 搜索
            'keyword' => $this->request->get('keyword', '', 'trim'),
            // 状态
            'status' => $this->request->get('status', '', 'trim'),
            // 分类
            'catid' => $this->request->get('catid', '', 'trim'),
            // 推荐位ID
            'posid' => $this->request->get('posid', '', 'trim'),
            // 时间范围搜索
            'time_type' => $this->request->get('time_type', '', 'trim'),
            'start_time' => $this->request->get('start_time', '', 'trim'),
            'end_time' => $this->request->get('end_time', '', 'trim'),
        ];
        $query = ArticleModel::withTrashed()->with(['category'])->order(['id' => 'desc']);
        $fun = function ($key, $value) use ($query, $param) {
            switch ($key) {
                case 'status':
                    $query->where($key, $value);
                    break;
                case 'catid':
                    $cate = Category::info($value);
                    if (empty($cate)) {
                        $query->where('catid', $value);
                    } else {
                        $query->whereIn('catid', $cate->arrchildid);
                    }
                    break;
                case 'posid':
                    $query->whereFindInSet('posid_ids', $value);
                    break;
                case 'keyword':
                    if (Validate::is($value, 'integer')) {
                        $query->where('id', $value);
                    } else {
                        $keywords = explode(' ', $value);
                        if (count($keywords) > 1) {
                            $likes = [];
                            foreach ($keywords as $k) {
                                $likes[] = "%{$k}%";
                            }
                            $query->whereLike('title', $likes);
                        } else {
                            $query->whereLike('title', "%{$value}%");
                        }
                    }
                    break;
                case 'start_time':
                case 'end_time':
                    $field = match ((int)$param['time_type']) {
                        1 => 'create_time',
                        2 => 'update_time',
                        default => 'release_time',
                    };
                    $time = strtotime($value);
                    if ($key == 'start_time') {
                        $query->where($field, '>=', strtotime(date('Y-m-d', $time)));
                    } else {
                        $query->where($field, '<=', strtotime(date('Y-m-d 23:59:59', $time)));
                    }
                    break;
            }
        };
        foreach ($param as $key => $value) {
            if ($value !== '') {
                $fun($key, $value);
            }
        }
        return $this->basePage($query, $this->getPageId(), null, null, $this->getLimit());
    }

    /**
     * 添加
     * @return ApiResult
     */
    public function add()
    {
        $data = [
            'catid' => $this->request->post('catid', 0, 'intval'),
            'title' => $this->request->post('title', '', 'trim'),
            'short_title' => $this->request->post('short_title', '', 'trim'),
            'status' => $this->request->post('status', 0, 'intval'),
            'tags' => $this->request->post('tags', '', 'trim'),
            'keywords' => $this->request->post('keywords', '', 'trim'),
            'thumb' => $this->request->post('thumb', '', 'trim'),
            'author' => $this->request->post('author', '乾元日历', 'trim'),
            'source' => $this->request->post('source', '互联网', 'trim'),
            'posid_ids' => $this->request->post('posid_ids', '', 'trim'),
            'description' => $this->request->post('description', '', 'trim'),
            'views' => $this->request->post('views', 0, 'intval'),
            'release_time' => $this->request->post('release_time', time(), 'trim'),
            'body' => $this->request->post('body', '', 'trim'),
        ];
        $model = new ArticleModel();
        if ($model->setOperator($this->user)->plus($data)) {
            return ApiResult::success(true);
        }
        return ApiResult::error($model->getError() ?: '添加失败');
    }

    /**
     * 编辑
     * @return ApiResult
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function edit()
    {
        $id = $this->request->post('id', 0, 'intval');
        /**
         * @var ArticleModel $info
         */
        $info = ArticleModel::where('id', $id)->find();
        if (empty($info)) {
            return ApiResult::error('数据不存在');
        }
        $data = [
            'catid' => $this->request->post('catid', null, 'intval'),
            'title' => $this->request->post('title', null, 'trim'),
            'short_title' => $this->request->post('short_title', null, 'trim'),
            'status' => $this->request->post('status', null, 'intval'),
            'tags' => $this->request->post('tags', null, 'trim'),
            'keywords' => $this->request->post('keywords', null, 'trim'),
            'thumb' => $this->request->post('thumb', null, 'trim'),
            'author' => $this->request->post('author', null, 'trim'),
            'source' => $this->request->post('source', null, 'trim'),
            'posid_ids' => $this->request->post('posid_ids', null, 'trim'),
            'description' => $this->request->post('description', null, 'trim'),
            'views' => $this->request->post('views', null, 'intval'),
            'release_time' => $this->request->post('release_time', null, 'trim'),
            'body' => $this->request->post('body', null, 'trim'),
        ];
        // 过滤掉值为 null 的元素
        $data = array_filter($data, function ($value) {
            return $value !== null;
        });
        if ($info->setOperator($this->user)->edit($data)) {
            // 投递刷新任务
            $task = [
                'id' => $info->id,
            ];
            Queue::connection()->push('article.ArticleRelevant', $task);
            return ApiResult::success(true);
        }
        return ApiResult::error($info->getError() ?: '编辑失败');
    }

    /**
     * 删除（数据库软删除）
     * @return bool|ApiResult
     */
    public function delete()
    {
        $id = $this->request->get('id', '', 'trim');
        if (empty($id)) {
            return ApiResult::error('ID不能为空');
        }
        return $this->baseDelete(ArticleModel::class, $id);
    }

    /**
     * 删除恢复
     * @return ApiResult
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function restore()
    {
        $id = $this->request->get('id', 0, 'intval');
        /**
         * @var ArticleModel $info
         */
        $info = ArticleModel::withTrashed()->where('id', $id)->find();
        if (empty($info)) {
            return ApiResult::error('数据不存在');
        }
        if ($info->rmRestore()) {
            return ApiResult::success(true);
        }
        return ApiResult::error($info->getError() ?: '删除恢复失败');
    }
}
