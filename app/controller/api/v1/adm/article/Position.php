<?php
// +----------------------------------------------------------------------
// | Position推荐位
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\api\v1\adm\article;

use api\admin\ApiBase;
use api\ApiResult;
use api\traits\controller\GeneralBaseCurdTraits;
use api\traits\controller\PageTraits;
use app\model\article\Position as PositionModel;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\facade\Validate;

class Position extends ApiBase
{
    use GeneralBaseCurdTraits;
    use PageTraits;

    /**
     * 权限映射
     * @var array
     */
    protected array $accessMap = [
        'public' => ['info', 'tree'],
        'position/list' => ['list'],
        'position/add' => ['add', 'edit'],
        'position/delete' => ['delete'],
    ];

    /**
     * 详情
     * @return ApiResult
     */
    public function info()
    {
        $id = $this->request->get('id', '', 'trim');
        if (empty($id)) {
            return ApiResult::error('ID不能为空');
        }
        return $this->baseInfo(PositionModel::class, $id);
    }

    /**
     * tree树
     * @return ApiResult
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function tree()
    {
        $platform = $this->request->get('platform', null, 'trim');
        $query = PositionModel::order(['listorder' => 'desc', 'id' => 'desc']);
        if (!is_null($platform)) {
            $query->where('platform', $platform);
        }
        $list = $query->field('id,name,platform')
            ->select();
        return ApiResult::success($list);
    }

    /**
     * 列表
     * @return ApiResult
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function list()
    {
        $param = [
            // 搜索
            'keyword' => $this->request->get('keyword', '', 'trim'),
            // 平台
            'platform' => $this->request->get('platform', '', 'trim'),
        ];
        $query = PositionModel::order(['listorder' => 'desc', 'id' => 'desc']);
        $fun = function ($key, $value) use ($query, $param) {
            switch ($key) {
                case 'platform':
                    $query->where($key, $value);
                    break;
                case 'keyword':
                    if (Validate::is($value, 'integer')) {
                        $query->where('id', $value);
                    } else {
                        $keywords = explode(' ', $value);
                        if (count($keywords) > 1) {
                            $likes = [];
                            foreach ($keywords as $k) {
                                $likes[] = "%{$k}%";
                            }
                            $query->whereLike('name', $likes);
                        } else {
                            $query->whereLike('name', "%{$value}%");
                        }
                    }
                    break;
            }
        };
        foreach ($param as $key => $value) {
            if ($value !== '') {
                $fun($key, $value);
            }
        }
        return $this->basePage($query, $this->getPageId(), null, null, $this->getLimit());
    }

    /**
     * 添加
     * @return ApiResult
     */
    public function add()
    {
        $data = [
            'name' => $this->request->post('name', '', 'trim'),
            'catid' => $this->request->post('catid', '', 'trim'),
            'max_num' => $this->request->post('max_num', '20', 'trim'),
            'platform' => $this->request->post('platform', '0', 'intval'),
            'listorder' => $this->request->post('listorder', '0', 'trim'),
        ];
        $model = new PositionModel();
        if ($model->plus($data)) {
            return ApiResult::success(true);
        }
        return ApiResult::error($model->getError() ?: '添加失败');
    }

    /**
     * 编辑
     * @return ApiResult
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function edit()
    {
        $id = $this->request->post('id', 0, 'intval');
        /**
         * @var PositionModel $info
         */
        $info = PositionModel::where('id', $id)->find();
        if (empty($info)) {
            return ApiResult::error('数据不存在');
        }
        $data = [
            'name' => $this->request->post('name', null, 'trim'),
            'catid' => $this->request->post('catid', null, 'trim'),
            'max_num' => $this->request->post('max_num', null, 'trim'),
            'platform' => $this->request->post('platform', null, 'intval'),
            'listorder' => $this->request->post('listorder', null, 'trim'),
        ];
        // 过滤掉值为 null 的元素
        $data = array_filter($data, function ($value) {
            return $value !== null;
        });
        if ($info->edit($data)) {
            return ApiResult::success(true);
        }
        return ApiResult::error($info->getError() ?: '编辑失败');
    }

    /**
     * 删除
     * @return bool|ApiResult
     */
    public function delete()
    {
        $id = $this->request->get('id', '', 'trim');
        if (empty($id)) {
            return ApiResult::error('ID不能为空');
        }
        return $this->baseDelete(PositionModel::class, $id);
    }
}
