<?php
// +----------------------------------------------------------------------
// | 测试-订单相关
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\api\v1\member\cesuan;

use api\ApiResult;
use api\traits\controller\GeneralBaseCurdTraits;
use api\traits\controller\PageTraits;
use app\model\cesuan\Order as CesuanOrderModel;
use app\model\cesuan\Order as CeuanOrderModel;
use app\traits\rpc\CesuanOrderTraits;
use member\UserBase;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\Exception;
use think\facade\Request;

class Order extends UserBase
{
    use GeneralBaseCurdTraits;
    use PageTraits;
    use CesuanOrderTraits;

    /**
     * 订单详情
     * @return ApiResult
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function info()
    {
        $id = Request::param('id', '', 'trim');
        if (empty($id)) {
            return ApiResult::error('订单ID或者编号不能为空');
        }
        $query = CeuanOrderModel::where('uid', $this->uid());
        if (is_numeric($id)) {
            $query->where('id', $id);
        } else {
            $query->where('order_sn', $id);
        }
        $info = $query->find();
        if (empty($info)) {
            return ApiResult::error('订单不存在');
        }
        $info->append(['server_url']);
        return ApiResult::success($info->toArray());
    }

    /**
     * 订单列表
     * @return ApiResult
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function list()
    {
        $param = [
            // 状态
            'status' => Request::param('status', '', 'trim'),
        ];
        $query = CeuanOrderModel::where('uid', $this->uid());
        $fun = function ($key, $value) use ($query) {
            switch ($key) {
                case 'status':
                    $query->where($key, $value);
                    break;
            }
        };
        foreach ($param as $key => $value) {
            if ($value != '') {
                $fun($key, $value);
            }
        }
        $query->append(['server_url']);
        return $this->basePage($query, $this->getPageId(), null, ['id' => 'desc'], $this->getLimit());
    }

    /**
     * 删除（数据库软删除）
     * @return ApiResult
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function delete()
    {
        $id = Request::param('id', '', 'trim');
        if (empty($id)) {
            return ApiResult::error('订单ID或者编号不能为空');
        }
        $query = CeuanOrderModel::where('uid', $this->uid());
        if (is_numeric($id)) {
            $query->where('id', $id);
        } else {
            $query->where('order_sn', $id);
        }
        $info = $query->find();
        if (empty($info)) {
            return ApiResult::error('订单不存在');
        }
        $info->delete();
        return ApiResult::success(true);
    }

    /**
     * 测算订单同步
     * @return ApiResult
     * @throws Exception
     */
    public function sync()
    {
        $orderSn = Request::get('order_sn', '', 'trim');
        if (empty($orderSn)) {
            return ApiResult::error('订单号不能为空');
        }
        $order = $this->orderInfo($orderSn);
        $newOrder = CesuanOrderModel::sync($order);
        return ApiResult::success($newOrder->bindUid($this->uid()));
    }
}
