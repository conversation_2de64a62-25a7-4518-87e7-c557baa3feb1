<?php
// +----------------------------------------------------------------------
// | 会员
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\api\v1\member\user;

use api\ApiResult;
use api\traits\controller\GeneralBaseCurdTraits;
use member\UserBase;
use think\facade\Request;

class User extends UserBase
{
    use GeneralBaseCurdTraits;

    /**
     * 当前会员信息
     * @return ApiResult
     */
    public function my()
    {
        // $this->user->append(['auth'], true);
        return ApiResult::success($this->user);
    }

    /**
     * 更新用户资料
     * @return bool
     */
    public function update()
    {
        $data = [
            'nickname' => Request::param('nickname', null, 'trim'),
            'avatar' => Request::param('avatar', null, 'trim'),
            // 性别：0未知，1男，2女
            'gender' => Request::param('gender', null, 'trim'),
            'birthday' => Request::param('birthday', null, 'trim'),
            'email' => Request::param('email', null, 'trim'),
        ];
        $data = array_filter(
            $data,
            function ($v) {
                return !is_null($v);
            }
        );
        return $this->baseEdit($this->user, $data);
    }
}
