<?php
// +----------------------------------------------------------------------
// | 登录相关
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\api\v1\member;

use api\ApiResult;
use app\model\user\User;
use app\model\user\User as UserModel;
use app\traits\MobileLoginVerificationCodeTraits;
use app\traits\PassDriverTraits;
use pass\Driver;
use pass\traits\TraitsAccessToken;
use think\facade\Cache;
use think\facade\Env;
use think\facade\Request;
use think\facade\Validate;

class Login
{
    use TraitsAccessToken;
    use PassDriverTraits;
    use MobileLoginVerificationCodeTraits;

    /**
     * 会员驱动
     * @var Driver
     */
    protected Driver $passService;

    /**
     * 初始化
     * Login constructor.
     */
    public function __construct()
    {
        // 实例化会员驱动
        $this->passService = User::passService($this->initDriver());
    }

    /**
     * 昵称是否存在
     * @return ApiResult
     */
    public function nicknameExist()
    {
        $nickname = Request::param('nickname', '', 'trim');
        if (empty($nickname)) {
            return ApiResult::error('昵称不能为空');
        }
        $isRepeat = UserModel::where('nickname', $nickname)
            ->value('id');
        if ($isRepeat) {
            return ApiResult::error('该昵称已被使用');
        }
        return ApiResult::success(true);
    }

    /**
     * 邮箱是否存在
     * @return ApiResult
     */
    public function emailExist()
    {
        $email = Request::param('email', '', 'trim');
        if (empty($email)) {
            return ApiResult::error('邮箱不能为空');
        }
        $isRepeat = UserModel::where('email', $email)
            ->value('id');
        if ($isRepeat) {
            return ApiResult::error('该邮箱已被使用');
        }
        return ApiResult::success(true);
    }

    /**
     * 手机验证码登录+注册
     * @return ApiResult
     */
    public function mobilelogin()
    {
        $data = [
            'devicetype' => Request::param('devicetype', '', 'trim'),
            'mobile' => Request::param('mobile', '', 'trim'),
            'captcha' => Request::param('captcha', '', 'trim'),
            'channel' => Request::param('channel', '', 'trim'),

            'scene' => Request::param('scene', '', 'trim'),
            'order_sn' => Request::param('order_sn', '', 'trim'),
        ];
        $validate = Validate::rule(
            [
                'devicetype|设备类型' => ['require'],
                'mobile|手机号' => ['require'],
                'captcha|验证码' => ['require'],
                'scene|场景' => ['alphaDash'],
                // 测算订单绑定手机号
                'order_sn|订单编号' => ['requireIf:scene,cesuan_order_bind'],
            ]
        );
        if (!$validate->check($data)) {
            return ApiResult::error($validate->getError());
        }
        /**
         * 闭包验证函数
         * @param string $mobile
         * @param string|int $code
         * @return bool
         */
        $checkSms = function ($mobile, $code) {
            // 线上测试账号
            if ($mobile == '13800002023' && $code == '080808') {
                return true;
            }
            if (Env::get('app_debug', false) && $code == '123456') {
                return true;
            }
            // 检查验证码
            $key = self::getverificationCodeKey($mobile);
            // 非调试模式下，校验验证码
            if (Cache::get($key) != $code) {
                return false;
            }
            Cache::delete($key);
            return true;
        };
        try {
            // 手机号验证码登录
            $res = $this->passService->mobileLogin($data['mobile'], $data['captcha'], $checkSms, $data['channel']);
            if (true !== $res) {
                return ApiResult::error($this->passService->getError() ?: '登录失败');
            }
        } catch (\Exception | \Throwable $e) {
            return ApiResult::error($e->getMessage() ?: '登录失败');
        }
        try {
            $token = $this->getAceessTokenInfo($this->passService->accessToken(null));
        } catch (\Exception | \Throwable $e) {
            return ApiResult::error($e->getMessage() ?: '获取 access_token 失败');
        }
        $data = [
            'token' => $token['token'],
            'expires_in' => $token['expires_in'],
        ];
        return ApiResult::success($data);
    }

    /**
     * 会员登录验证短信发送接口
     * @return ApiResult
     */
    public function sendVerificationCode()
    {
        $phone = Request::param('phone', '', 'trim');
        if ($this->verificationCodeSend($phone)) {
            return ApiResult::success(true);
        }
        return ApiResult::error($this->getError() ?: '发送验证码失败');
    }
}
