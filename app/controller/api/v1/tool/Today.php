<?php
// +----------------------------------------------------------------------
// | 历史上的今天
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\api\v1\tool;

use api\ApiResult;
use api\traits\controller\GeneralBaseCurdTraits;
use api\traits\controller\PageTraits;
use app\model\tool\Today as TodayModel;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\facade\Request;

class Today
{
    use GeneralBaseCurdTraits;
    use PageTraits;

    /**
     * 列表
     * @return ApiResult
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function list()
    {
        $month = Request::param('month', 1, 'intval');
        $day = Request::param('day', 1, 'intval');
        $query = TodayModel::where('month', $month)
            ->where('day', $day)
            ->order('date', 'desc');
        return $this->basePage(TodayModel::class, $this->getPageId(), $query, null, $this->getLimit());
    }
}
