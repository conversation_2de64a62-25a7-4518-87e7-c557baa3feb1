<?php
// +----------------------------------------------------------------------
// | 意见反馈
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\api\v1\tool;

use api\ApiResult;
use api\traits\controller\GeneralBaseCurdTraits;
use api\traits\controller\PageTraits;
use app\model\tool\Feedback as FeedbackModel;
use think\facade\Request;
use think\facade\Validate;

class Feedback
{
    use GeneralBaseCurdTraits;
    use PageTraits;

    /**
     * 提交
     * @return ApiResult
     */
    public function submit()
    {
        $data = [
            'content' => Request::post('content', '', 'trim'),
            'pic' => Request::post('pic/a', [], 'trim'),
        ];
        $validate = Validate::rule(
            [
                'content|反馈内容' => ['require'],
                'pic|图片' => ['array', function ($value, $data) {
                    if (empty($value)) {
                        return true;
                    }
                    foreach ($value as $v) {
                        if (empty($v['url'])) {
                            return '图片url不能为空';
                        }
                        if (empty($v['title'])) {
                            return '图片标题不能为空';
                        }
                    }
                    return true;
                }],
            ]
        );
        if (!$validate->check($data)) {
            return ApiResult::error($validate->getError());
        }
        $feedback = FeedbackModel::create($data);
        if (!$feedback->isExists()) {
            return ApiResult::error($feedback->getError() ?: '提交失败');
        }
        return ApiResult::success(true);
    }
}
