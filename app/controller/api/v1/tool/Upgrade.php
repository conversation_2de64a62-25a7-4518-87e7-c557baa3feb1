<?php
// +----------------------------------------------------------------------
// | 应用更新
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\api\v1\tool;

use api\ApiResult;
use app\model\tool\Upgrade as UpgradeModel;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\facade\Request;

class Upgrade
{

    /**
     * 检测更新
     * @return ApiResult
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function checkUpdate()
    {
        $appVersion = Request::param('app_version', '', 'trim');
        $deviceType = Request::param('devicetype', '', 'trim');
        if (empty($appVersion)) {
            return ApiResult::error('app当前版本号不能为空');
        }
        if (empty($deviceType)) {
            return ApiResult::error('app所属平台不能为空');
        }
        // 平台：1安卓，2苹果，3鸿蒙，多个逗号间隔
        $platform = match ($deviceType) {
            'ios_mp', 'ios_app' => 2,
            'android_mp', 'android_app' => 1,
            default => 0
        };
        $appVersionInt = UpgradeModel::versionToNumber($appVersion);
        $upgrade = UpgradeModel::where('status', 1)
            ->where('version_int', '>', $appVersionInt)
            ->whereFindInSet('platform', $platform)
            ->order(['version_int' => 'desc'])
            ->find();
        if (empty($upgrade)) {
            $data = [
                'app_version' => $appVersion,
                'new_version' => UpgradeModel::where('status', 1)
                    ->whereFindInSet('platform', $platform)
                    ->value('version'),
            ];
            return ApiResult::error('没有新版本', 0, $data);
        }
        return ApiResult::success($upgrade->toArray());
    }
}
