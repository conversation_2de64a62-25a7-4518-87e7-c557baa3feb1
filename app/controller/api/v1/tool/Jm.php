<?php
// +----------------------------------------------------------------------
// | 解梦
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\api\v1\tool;

use api\ApiResult;
use api\traits\controller\GeneralBaseCurdTraits;
use api\traits\controller\PageTraits;
use app\model\tool\Jm as JmModel;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\facade\Request;

class Jm
{
    use GeneralBaseCurdTraits;
    use PageTraits;

    /**
     * 列表
     * @return ApiResult
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function list()
    {
        $query = JmModel::where('status', 1)
            ->withoutField(['content']);
        return $this->basePage(JmModel::class, $this->getPageId(), $query, ['id' => 'desc'], $this->getLimit());
    }

    /**
     * 详情
     * @return ApiResult
     */
    public function info()
    {
        $id = Request::param('id', '', 'trim');
        return $this->baseInfo(JmModel::class, $id, 'info');
    }
}
