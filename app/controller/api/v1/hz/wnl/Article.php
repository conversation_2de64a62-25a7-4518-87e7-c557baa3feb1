<?php
// +----------------------------------------------------------------------
// | 万年历-文章管理
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\api\v1\hz\wnl;

use api\ApiResult;
use api\traits\controller\GeneralBaseCurdTraits;
use api\traits\controller\PageTraits;
use app\model\hz\wnl\Article as ArticleModel;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\facade\Request;
use think\model\Collection;

class Article
{
    use GeneralBaseCurdTraits;
    use PageTraits;

    /**
     * 无需鉴权
     * @var bool
     */
    protected bool $noVerification = true;

    /**
     * 列表
     * @return ApiResult
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function list()
    {
        $query = ArticleModel::where('status', 1);
        /**
         * @var Collection|ArticleModel[] $list
         */
        $list = $query->order(['release_time' => 'desc', 'id' => 'desc'])
            ->limit($this->getLimit(100))
            ->append(['category_name'])
            ->select();
        $listData = [];
        foreach ($list as $item) {
            $listData[] = [
                'id' => '',
                'category' => $item->category_name,
                'title' => $item->title,
                'covertype' => 0,
                'coverphoto' => '',
                'url' => '',
                'creationtime' => date('Y-m-d H:i:s', $item->release_timet),
                'author' => $item->author,
                'source' => $item->source ?: '乾元日历',
                'description' => preg_replace("/<(\/?a.*?)>/si", '', $item->content),
            ];
        }
        return ApiResult::Success($listData);
    }

    /**
     * 详情
     * @return ApiResult
     */
    public function info()
    {
        $id = Request::get('id');
        return $this->baseInfo(ArticleModel::class, $id, 'info');
    }
}
