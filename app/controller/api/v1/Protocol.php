<?php
// +----------------------------------------------------------------------
// | 协议
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\api\v1;

use api\ApiResult;
use app\model\tool\Protocol as ProtocolModel;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\model\Collection;

class Protocol
{
    /**
     * 获取全部协议
     * @return ApiResult
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function all()
    {
        /**
         * @var Collection|ProtocolModel[] $list
         */
        $list = ProtocolModel::order('id', 'desc')
            ->limit(20)
            ->select();
        if ($list->isEmpty()) {
            return ApiResult::success([]);
        }
        return ApiResult::success($list->column(null, 'at'));
    }
}
