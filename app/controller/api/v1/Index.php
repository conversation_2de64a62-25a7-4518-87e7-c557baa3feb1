<?php
// +----------------------------------------------------------------------
// | Index
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\api\v1;

use api\ApiResult;
use think\facade\Request;

class Index
{
    /**
     * 无需鉴权
     * @var bool
     */
    protected bool $noVerification = true;

    /**
     * dev
     * @return ApiResult
     */
    public function dev()
    {
        $data = [
            'isSsl' => Request::isSsl(),
            'isWeixinRequest' => Request::isWeixinRequest(),
            'isAndroid' => Request::isAndroid(),
            'isIos' => Request::isIos(),
            'isBaiduApp' => Request::isBaiduApp(),
            'isBaiduMapp' => Request::isBaiduMapp(),
            'isBaiduBigMapp' => Request::isBaiduBigMapp(),
            'isAlipayRequest' => Request::isAlipayRequest(),
            'getBrowser' => Request::getBrowser(),
            'domain' => Request::domain(),
            'server' => Request::server(),
            'header' => Request::header(),
            'cookie' => Request::cookie(),
        ];
        return ApiResult::success($data);
    }
}
