<?php
// +----------------------------------------------------------------------
// | TestBase 单元测试基类
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace tests\connector;

use PHPUnit\Framework\Assert as PHPUnit;
use think\facade\App;
use think\facade\Db;
use think\facade\Request;
use think\testing\traits\SwooleRpcTrait;

define('PHPUnit', true);

abstract class TestBase extends \think\testing\TestBase
{
    use SwooleRpcTrait;

    /**
     * 基础路径
     * @var string
     */
    protected string $baseUrl = 'https://www.qyapp.com';

    /**
     * 初始化
     * @return void
     */
    public function initialize()
    {
    }

    /**
     * 单页常规GET访问测试
     * @param string $urlPath 请求路径
     * @param array $headers
     */
    protected function pageGet(string $urlPath, array $headers = [])
    {
        $server = [];
        // 如果是手机模式下
        if ($this->isMobile) {
            $server['HTTP_USER_AGENT'] = 'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1';
        } else {
            $server['HTTP_USER_AGENT'] = 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_13_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/68.0.3440.106 Safari/537.36';
        }
        $this->withServerVariables($server);
        $server = $this->transformHeadersToServerVars($headers);
        $this->call('GET', $urlPath, [], $this->cookie ?? [], [], $server);
        if (in_array($this->response->getCode(), [301, 302])) {
            return;
        }
        // 断言访问状态
        $this->assertResponseOk();
        // 断言是否为 Response 对象
        if ($this->response instanceof \think\Response) {
            $this->responseEquals();
        }
        // 是否是$this->error错误页
        $this->isErrorContent();
    }

    /**
     * 断言响应状态
     * @return void
     */
    public function assertResponseOk()
    {
        $actual = $this->response->getCode();
        // 断言是否为真
        PHPUnit::assertTrue(200 == $actual, "预期的状态码 200, 得到 {$actual}. {$this->currentUri}");
    }

    /**
     * 断言页面是否为$this->error页面
     * @return void
     */
    protected function isErrorContent()
    {
        if (empty($this->response)) {
            return;
        }
        // 获取地址
        $url = Request::url(true);
        $content = $this->response->getContent();
        $systemMessage = strpos($content, '.shuipf-massage-error') || strpos($content, '.shuipf-massage-500');
        $message = "地址 {$url} 页面提示错误";
        if (false !== $systemMessage) {
            preg_match("/<p>(.*?)<\/p>/", $content, $m);
            if (isset($m[1])) {
                $message .= " ，错误提示：{$m[1]}";
            }
        }
        $this->assertEquals(false, $systemMessage, $this->error($message));
    }

    /**
     * 输出response内容
     * @return void
     */
    protected function dumpResponse()
    {
        $json = json_decode($this->response->getContent(), true);
        if (json_last_error()) {
            $json = $this->response->getContent();
        }
        dump($json);
    }

    /**
     * step 2
     * 执行单元测试前执行此方法
     */
    protected function setUp(): void
    {
        parent::setUp();
        $this->initRpcClient();
        $this->initialize();
    }

    /**
     * step 1
     * 在运行此测试类的第一个测试之前调用此方法
     */
    public static function setUpBeforeClass(): void
    {
        Db::startTrans();
    }

    /**
     * step 4
     * 在运行此测试类的最后一次测试后调用此方法
     */
    public static function tearDownAfterClass(): void
    {
        Db::rollback();
        $instances = ['log', 'session', 'view', 'response', 'cookie', 'http', 'request'];
        foreach ($instances as $instance) {
            App::delete($instance);
        }
        gc_collect_cycles();
    }
}
