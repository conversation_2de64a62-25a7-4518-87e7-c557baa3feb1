<?php
// +----------------------------------------------------------------------
// | ApiAdmBase
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace tests\connector;

use pass\exception\AccessTokenException;
use pass\interfaces\User as InterfacesUser;
use pass\objects\User as UserPass;
use rpc\contract\nadm\UserInterface;
use think\Collection;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\exception\ClassNotFoundException;
use think\facade\App;
use think\facade\Config;
use think\facade\Db;
use think\facade\Env;
use think\helper\Str;

class ApiAdmBase extends ApiBase
{
    /**
     * @var UserPass|null
     */
    protected ?UserPass $user;

    /**
     * @return void
     */
    public function initialize()
    {
        $this->getAdmUser();
    }

    /**
     * 获取后台用户
     * @param bool $isRand
     * @return InterfacesUser
     */
    protected function getAdmUser(bool $isRand = false): InterfacesUser
    {
        if (!empty($this->user) && !$isRand) {
            return $this->user;
        }
        /**
         * @var UserInterface $rpc
         */
        $rpc = App::make(UserInterface::class);
        $list = $rpc->getlist(1);
        $list = new Collection($list);
        if ($isRand) {
            $info = $list->shuffle()->first();
        } else {
            $info = $list->first();
        }
        $info['uid'] = $info['id'];
        $user = new UserPass($info);
        if (!$isRand) {
            $this->user = $user;
        }
        return $user;
    }

    /**
     * 获得授权
     * @param int $uid 用户UID
     * @return string
     * @throws AccessTokenException
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected function getAccessToken(int $uid): string
    {
        try {
            $database = Config::get('database', []);
            $database['connections'] ['admin'] = [
                // 数据库类型
                'type' => Env::get('database.type', 'mysql'),
                // 服务器地址
                'hostname' => Env::get('database.hostname', '127.0.0.1'),
                // 数据库名
                'database' => 'admin',
                // 用户名
                'username' => Env::get('database.username', 'root'),
                // 密码
                'password' => Env::get('database.password', ''),
                // 端口
                'hostport' => Env::get('database.hostport', '3306'),
                // 数据库连接参数
                'params' => [],
                // 数据库编码默认采用utf8
                'charset' => Env::get('database.charset', 'utf8'),
                // 数据库表前缀
                'prefix' => 'bw_',
                // 数据库部署方式:0 集中式(单一服务器),1 分布式(主从服务器)
                'deploy' => 0,
                // 是否严格检查字段是否存在
                'fields_strict' => true,
                // 是否需要断线重连
                'break_reconnect' => true,
                // 监听SQL
                'trigger_sql' => Env::get('app_debug', false),
                // 开启字段缓存
                'fields_cache' => true,
            ];
            Config::set($database, 'database');
            $tokenInfo = Db::connect('admin')
                ->name('access_token')
                ->where('uid', $uid)
                ->cache(120)
                ->find();
            $token = $tokenInfo['token'] ?? '';
            if (empty($tokenInfo)) {
                $token = Str::random(32);
                $data = [
                    'uid' => $uid,
                    'token' => $token,
                    'expires_in' => time() + (86400 * 7),
                    'relation_id' => 0,
                ];
                Db::connect('admin')
                    ->name('access_token')
                    ->save($data);
            } elseif ($tokenInfo['expires_in'] <= time() - 120) {
                // 刷新缓存
                Db::connect('admin')
                    ->name('access_token')
                    ->where('id', $tokenInfo['id'])
                    ->save(['expires_in' => time() + (86400 * 7)]);
            }
            return $token;
        } catch (ClassNotFoundException $e) {
            throw new AccessTokenException('令牌生成失败');
        }
    }

    /**
     * 获取api必要参数
     * @param int $appId 接口秘钥app_id
     * @return array
     * @throws AccessTokenException
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected function apiParameters(int $appId)
    {
        $parameters = [];
        $parameters['request_time'] = time();
        $parameters['app_id'] = $appId;
        $parameters['access_token'] = $this->getAccessToken($this->getAdmUser()->uid());
        return $parameters;
    }
}
