<?php
// +----------------------------------------------------------------------
// | ApiAdmBase
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace tests\connector;

use app\model\user\User;
use pass\driver\Qyapp;
use pass\exception\AccessTokenException;
use pass\interfaces\User as InterfacesUser;
use rpc\contract\member\AccessTokenInterface;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\exception\ClassNotFoundException;
use think\facade\App;
use think\facade\Cache;

class ApiMemberBase extends ApiBase
{
    /**
     * @var InterfacesUser|null
     */
    protected ?InterfacesUser $member;

    /**
     * 前台用户id
     * @var int
     */
    protected int $memberId = 0;

    /**
     * @return void
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function initialize()
    {
        $this->getMemberUser();
    }

    /**
     * 获取用户
     * @return InterfacesUser
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected function getMemberUser(): InterfacesUser
    {
        if (!empty($this->member)) {
            return $this->member;
        }
        $query = User::where('status', 1);
        if ($this->memberId) {
            $query->where('id', $this->memberId);
        }
        $this->member = $query->find();
        return $this->member;
    }

    /**
     * 获得授权
     * @param int $uid 用户UID
     * @return string
     * @throws AccessTokenException
     */
    protected function getAccessToken(int $uid): string
    {
        try {
            $cacheKeyName = "phpunit/access_token/{$uid}";
            Cache::delete($cacheKeyName);
            $cache = Cache::get($cacheKeyName, null);
            if (empty($cache)) {
                /**
                 * @var AccessTokenInterface $rpc
                 */
                $rpc = App::make(AccessTokenInterface::class);
                $accessToken = $rpc->createToken($uid, 10, Qyapp::class, true, 5);
                if (empty($accessToken)) {
                    throw new AccessTokenException('令牌生成失败');
                }
                $cache = $accessToken['token'];
                Cache::set($cacheKeyName, $cache, 120);
            }
            return $cache;
        } catch (ClassNotFoundException $e) {
            throw new AccessTokenException('令牌生成失败');
        }
    }

    /**
     * 获取api必要参数
     * @param int $appId 接口秘钥app_id
     * @return array
     * @throws AccessTokenException
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected function apiParameters(int $appId)
    {
        $parameters = [];
        $parameters['request_time'] = time();
        $parameters['app_id'] = $appId;
        $parameters['access_token'] = $this->getAccessToken($this->getMemberUser()->uid());
        return $parameters;
    }
}
