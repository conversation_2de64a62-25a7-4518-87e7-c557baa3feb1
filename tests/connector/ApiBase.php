<?php
// +----------------------------------------------------------------------
// |  ApiBaseTest
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace tests\connector;

use core\traits\ErrorClass;
use tests\traits\ApiInterfaceTrait;
use tests\traits\TestBaseTrait;
use tests\traits\ValidationTraits;

class ApiBase extends TestBase
{
    use ApiInterfaceTrait;
    use ValidationTraits;
    use TestBaseTrait;
    use ErrorClass;

    /**
     * 请求地址公共部分
     * @var string
     */
    protected string $commonPath = '';

    /**
     * 接口秘钥app_id
     * @var int
     */
    protected int $appId = 2;

    /**
     * 接口秘钥secret
     * @var string
     */
    protected string $appSecret = 'asdljklxjc12897SDKJXDsd046564xhd';
}
