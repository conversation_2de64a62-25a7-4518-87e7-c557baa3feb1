<?php
// +----------------------------------------------------------------------
// | TestBaseTrait
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace tests\traits;

use app\model\BaseModel as Model;
use pass\exception\AccessTokenException;
use think\Collection;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\db\Query;
use think\Exception;

trait TestBaseTrait
{
    /**
     * 基础添加测试用例
     * @param string|Model $model
     * @param string $url
     * @param array $data
     * @return mixed
     * @throws Exception
     * @throws AccessTokenException
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function baseAddTest(string | Model $model, string $url, array $data): mixed
    {
        $model = $this->getModelObject($model);
        $pk = $model->getPk();
        $this->pagePost($url, $data);

        $this->assertNotEmpty($this->response->getData()['data'] ?? false, '新增测试用例失败');

        return $model->order($pk, 'desc')->find();
    }

    /**
     * 基础列表测试用例
     * @param string $url
     * @param array $query
     * @param array $rule
     * @param bool $return
     * @return mixed|Collection|void
     * @throws AccessTokenException
     */
    public function baseListTest(string $url, array $query = [], array $rule = [], bool $return = true)
    {
        if ($query) {
            $parseUrl = parse_url($url);
            parse_str($parseUrl['query'] ?? '', $queryOrigin);
            $query = array_merge($queryOrigin, $query);
            $url .= '?' . http_build_query($query);
        }
        $this->pageGet($url);
        $this->validationResponseData($rule);
        if ($return) {
            return $this->response->getData()['data']['data'] ?? new Collection();
        }
    }

    /**
     * 基础详情测试用例
     * @param string|Model $model
     * @param string $url
     * @param array $rule 校验规则
     * @return void
     * @throws AccessTokenException
     * @throws Exception
     */
    public function baseInfoTest(string | Model $model, string $url, array $rule = [])
    {
        $model = $this->getModelObject($model);

        $query = [
            $model->getPk() => $model->getKey(),
        ];
        $this->pageGet($url . '?' . http_build_query($query));

        $result = $this->response->getData()['data'] ?? [];

        if ($result instanceof Model) {
            $result = $result->toArray();
        }

        $this->assertEquals($model->toArray(), $result);

        if ($rule) {
            $this->validationResponseData($rule);
        }
    }

    /**
     * 基础批量编辑测试用例
     * @param Collection $collection
     * @param string $url
     * @param array $data
     * @param array $checkData
     * @param bool $pkIsArr
     * @return void
     * @throws AccessTokenException
     */
    public function baseBatchEditTest(Collection $collection, string $url, array $data = [], array $checkData = [], bool $pkIsArr = true)
    {
        if (!$checkData) {
            $checkData = $data;
        }

        $pkValues = $collection->column('id');
        $data['ids'] = $pkIsArr ? $pkValues : implode(',', $pkValues);

        $this->pagePost($url, $data);
        $hidden = ['update_time'];
        $visible = array_keys($checkData);
        foreach ($collection as $model) {
            /**
             * @var Model $model
             */
            $model->refresh();

            $model->hidden($hidden);
            $model->visible($visible);

            $originModel = clone $model;

            // 设置修改的字段
            $originModel->setAttrs($checkData);
            $originModel->hidden($hidden);

            $this->assertEquals($originModel->toArray(), $model->toArray(), '批量更新测试失败：' . $model->getError());
        }
    }

    /**
     * 基础编辑测试用例
     * @param string|Model $model
     * @param string $url
     * @param array $data
     * @param array $checkData
     * @return void
     * @throws AccessTokenException
     * @throws Exception
     */
    public function baseEditTest(string | Model $model, string $url, array $data, array $checkData = [])
    {
        $model = $this->getModelObject($model);

        if (!$checkData) {
            $checkData = $data;
        }

        // 根据主键编辑
        $data[$model->getPk()] = $model->getKey();

        // 请求接口
        $this->pagePost($url, $data);

        // 过滤掉未在数据表中的字段，有可能参数名和字段名不一致的情况
        $tableFields = $model->getTableFields();
        foreach ($checkData as $key => $value) {
            if (!in_array($key, $tableFields)) {
                unset($checkData[$key]);
            }
        }

        $hidden = ['update_time'];
        $visible = array_keys($checkData);

        // 刷新模型数据
        $model->refresh();
        $model->hidden($hidden);
        $model->visible($visible);

        $originModel = $model->clone();
        // 设置修改的字段
        foreach ($checkData as $key => $value) {
            $originModel->setAttr($key, $value);
        }
        $originModel->hidden($hidden);

        $this->assertNotEmpty($this->response->getData()['data'] ?? false, '编辑操作测试失败：' . $model->getError());
        $this->assertEquals($originModel->toArray(), $model->toArray());
    }

    /**
     * 基础删除测试用例
     * @param string|Model $model
     * @param string $url
     * @return void
     * @throws AccessTokenException
     * @throws DataNotFoundException
     * @throws DbException
     * @throws Exception
     * @throws ModelNotFoundException
     */
    public function baseDeleteTest(string | Model $model, string $url)
    {
        $model = $this->getModelObject($model);

        $data = [
            $model->getPk() => $model->getKey(),
        ];

        $this->pagePost($url, $data);

        $this->assertTrue($this->response->getData()['data'] ?? false);
        $this->assertEmpty($model->find($data['id']), '删除测试用例失败' . $model->getError());
    }

    /**
     * 基础批量删除测试用例
     * @param Collection|array $collection
     * @param string|Model $model
     * @param string $url
     * @return void
     * @throws AccessTokenException
     * @throws Exception
     */
    public function baseBatchDeleteTest(Collection | array $collection, string | Model $model, string $url)
    {
        if (!$collection instanceof Collection) {
            $collection = new Collection($collection);
        }

        $model = $this->getModelObject($model);
        $ids = $collection->column('id');
        $data = [
            'ids' => implode(',', $ids),
        ];

        $this->pagePost($url, $data);

        $this->assertEquals(0, $model->whereIn('id', $ids)->count('id'), '批量删除测试用例失败' . $model->getError());
    }

    /**
     * 创建测试数据
     * @param string|Model|Query $model
     * @param array $data
     * @param string $func
     * @return mixed
     * @throws DataNotFoundException
     * @throws DbException
     * @throws Exception
     * @throws ModelNotFoundException
     */
    public function getModelForTest(string | Model | Query $model, array $data, string $func = 'save'): mixed
    {
        if ($model instanceof Query) {
            $query = $model;
            $model = $query->getModel();
            if (empty($model)) {
                throw new Exception('获取 model 对象失败');
            }
            $object = $query->find();
        } else {
            $model = $this->getModelObject($model);
            $object = $model->order('id', 'desc')->find();
        }

        if (!empty($object)) {
            return $object;
        }

        if (!$model->$func($data)) {
            throw new Exception("创建{$model}失败：" . $model->getError());
        }

        return $model;
    }

    /**
     * 返回模型对象
     * @param string|Model $model 模型名称
     * @return Model
     * @throws Exception
     */
    private function getModelObject(string | Model $model): Model
    {
        //模型方式
        if (is_string($model) && strpos($model, '/')) {
            [$app, $mo] = explode('/', $model, 2);
            $class = "\\app\\{$app}\\model\\" . implode('\\', explode('.', $mo));
            $model = new $class();
        } elseif (is_string($model) && strpos($model, '\\')) {//类方式
            $model = new $model();
        }
        if ($model instanceof Model) {
            return $model;
        }
        throw new Exception('$model 不是模型对象');
    }
}
