<?php
// +----------------------------------------------------------------------
// | ValidationTraits
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace tests\traits;

use think\Collection;
use think\facade\Validate;
use think\helper\Arr;
use think\helper\Str;

trait ValidationTraits
{
    /**
     * 验证方法数据
     * @param array $rule
     * @return void
     */
    protected function validationResponseData(array $rule): void
    {
        $json = json_decode($this->response->getContent(), true);
        if (json_last_error()) {
            $json = $this->response->getContent();
        }
        $this->assertIsArray($json);
        $this->assertTrue(
            $this->validation($json, $rule),
            '数据验证未通过：' . $this->getError()
        );
    }

    /**
     * 验证器
     * @param array|Collection $jsonData
     * @param array $rule
     * @param string $prefix
     * @param Collection|null $originalJsonData
     * @return bool
     */
    protected function validation(array | Collection $jsonData, array $rule, string $prefix = '', Collection $originalJsonData = null): bool
    {
        if (!$jsonData instanceof Collection) {
            $jsonData = new Collection($jsonData);
        }
        if (is_null($originalJsonData)) {
            $originalJsonData = $jsonData;
        }
        if ($jsonData->isEmpty()) {
            return true;
        }
        if (empty($prefix)) {
            foreach ($rule as $field => $rules) {
                if (in_array('require', $rules)) {
                    // 是否有requireWith先决条件
                    if (Str::contains($rules[0], 'requireWith:')) {
                        $requireWith = explode(':', $rules[0]);
                        // 如果依赖的字段不存在，或者为空，跳过
                        if (
                            !Arr::has($jsonData->toArray(), $requireWith[1]) ||
                            (Arr::has($jsonData->toArray(), $requireWith[1]) && empty(Arr::get($jsonData->toArray(), $requireWith[1], true)))
                        ) {
                            continue;
                        }
                    } elseif (Str::contains($rules[0], 'requireIf:')) {
                        $requireIf = explode(':', $rules[0]);
                        $requireIfValue = explode(',', $requireIf[1]);
                        // 如果依赖的字段不存在，或者不等于约定的值，则跳过
                        if (
                            !Arr::has($jsonData->toArray(), $requireIfValue[0]) ||
                            (Arr::has($jsonData->toArray(), $requireIfValue[0]) && Arr::get($jsonData->toArray(), $requireIfValue[0]) != $requireIfValue[1])
                        ) {
                            continue;
                        }
                    }
                    $fieldArr = explode('|', $field);
                    $field = $fieldArr[0];
                    $map = $jsonData;
                    foreach (explode('.', $field) as $segment) {
                        if (!isset($map[$segment])) {
                            $this->error = "字段 {$field} 不存在\n返回数据:\n" . json_encode($jsonData->toArray(), JSON_UNESCAPED_UNICODE);
                            return false;
                        } else {
                            $map = $map[$segment];
                        }
                    }
                }
            }
        }
        foreach ($jsonData as $key => $item) {
            $keyName = "{$prefix}{$key}";
            if (
                (
                    is_array($item) ||
                    $item instanceof Collection
                ) &&
                true != $this->validation($item, $rule, "{$keyName}.", $originalJsonData)
            ) {
                return false;
            }
            if (!isset($rule[$keyName])) {
                continue;
            }
            $vData = [
                '_current' => $item,
            ];
            $vRules = [
                "_current|{$keyName}" => $rule[$keyName],
            ];
            $validate = Validate::rule($vRules);
            // 合并原始数据，用于关联验证
            $validateData = array_merge($originalJsonData->toArray(), $vData);
            // 过滤掉null的字段
            $validateData = array_filter(
                $validateData,
                function ($v) {
                    return !is_null($v);
                }
            );
            if (!$validate->check($validateData)) {
                $this->error = $validate->getError() . "\n验证字段：{$keyName}\n验证规则：" . var_export($rule[$keyName], true) . "\n字段值：" . var_export($item, true);
                return false;
            }
        }
        return true;
    }
}
