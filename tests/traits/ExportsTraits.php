<?php
// +----------------------------------------------------------------------
// | ExportTraits
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace tests\traits;

use pass\exception\AccessTokenException;
use think\helper\Str;

trait ExportsTraits
{
    /**
     * 初始化测试用例
     * @return String
     * @throws AccessTokenException
     */
    public function testReady1(): string
    {
        $data = $this->getExportParam();

        $this->pagePost($this->commonPath . 'ready1.html', $data);

        $this->validationResponseData(
            [
                'data.count' => ['require', 'number'],
                'data.limit' => ['require', 'number'],
                'data.export' => ['require', 'alphaNum'],
            ]
        );

        return $this->response->getData()['data']['export'] ?? Str::random();
    }

    /**
     * 处理数据测试用例
     * @depends testReady1
     * @param String $export
     * @return String
     * @throws AccessTokenException
     */
    public function testReady2(string $export): string
    {
        $data = [
            'export' => $export,
        ];

        $this->pagePost($this->commonPath . 'ready2.html', $data);

        $this->validationResponseData(
            [
                'data.done' => ['require', 'in:0,1'],
                'data.result' => ['requireIf:data.done,0'],
                'data.result.success' => ['requireWith:data.result', 'require', 'number'],
                'data.result.total' => ['requireWith:data.result', 'require', 'number'],
            ]
        );

        return $this->response->getData()['data']['export'] ?? Str::random();
    }

    /**
     * 获取压入数据现有长度测试用例
     * @depends testReady1
     * @param String $export
     * @return String
     * @throws AccessTokenException
     */
    public function testReady3(string $export): string
    {
        $data = [
            'export' => $export,
        ];

        $this->pagePost($this->commonPath . 'ready3.html', $data);

        $this->validationResponseData(
            [
                'data.length' => ['require', 'number'],
            ]
        );

        return $this->response->getData()['data']['export'] ?? Str::random();
    }

    /**
     * 生成文件测试用例
     * @depends testReady1
     * @param String $export
     * @throws AccessTokenException
     */
    public function testReady4(string $export)
    {
        $data = [
            'export' => $export,
        ];

        $this->pagePost($this->commonPath . 'ready4.html', $data);

        $this->validationResponseData(
            [
                'data.done' => ['in:0,1'],
                'data.append_id' => ['requireIf:data.done,0', 'require'],
                'data.object' => ['requireIf:data.done,0', 'require'],
                'data.file' => ['requireIf:data.done,1', 'require', 'url'],
            ]
        );
    }

    /**
     * 获得导出查询条件
     * @return array
     */
    protected function getExportParam(): array
    {
        return [
            'start_time' => date('Y-m-d', strtotime("-1 day")),
            'end_time' => date('Y-m-d'),
        ];
    }
}
