<?php
// +----------------------------------------------------------------------
// |  ApiInterfaceTrait
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace tests\traits;

use common\lib\Helper;
use Exception;
use pass\exception\AccessTokenException;
use rpc\contract\api\ApiInterface;
use think\facade\App;

trait ApiInterfaceTrait
{
    /**
     * 单页常规GET访问测试
     * @param string $urlPath 请求路径
     * @param array $headers
     * @throws AccessTokenException|Exception
     */
    protected function pageGet(string $urlPath, array $headers = [])
    {
        $server = [];
        // 如果是手机模式下
        if ($this->isMobile) {
            $server['HTTP_USER_AGENT'] = 'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1';
        } else {
            $server['HTTP_USER_AGENT'] = 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_13_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/68.0.3440.106 Safari/537.36';
        }
        $this->withServerVariables($server);
        $parseUrl = parse_url($urlPath);
        parse_str($parseUrl['query'] ?? '', $query);
        $query += $this->apiParameters($this->appId);
        $query['sign2'] = $this->signature($this->appId, $query);
        $urlPath = Helper::urlBuild($urlPath, $query, true);
        $this->get($urlPath, $headers);
        if (in_array($this->response->getCode(), [301, 302])) {
            return;
        }
        $this->assertResponseOk();
        // 断言是否为 Response 对象
        if ($this->response instanceof \think\Response) {
            $this->responseEquals();
        }
        // 是否是$this->error错误页
        $this->isErrorContent();
        // 断言接口
        $this->checkResponseData();
    }

    /**
     * 单页常规Post访问测试
     * @param string $urlPath 请求路径
     * @param array $data 提交数据
     * @param array $headers
     * @param array $files
     * @throws AccessTokenException|Exception
     */
    protected function pagePost(string $urlPath, array $data = [], array $headers = [], array $files = [])
    {
        $server = [];
        // 如果是手机模式下
        if ($this->isMobile) {
            $server['HTTP_USER_AGENT'] = 'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1';
        } else {
            $server['HTTP_USER_AGENT'] = 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_13_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/68.0.3440.106 Safari/537.36';
        }
        $this->withServerVariables($server);
        $query = $data;
        $query += $this->apiParameters($this->appId);
        $query['sign2'] = $this->signature($this->appId, $query);
        $this->post($urlPath, $query, $headers, $files);
        if (in_array($this->response->getCode(), [301, 302])) {
            return;
        }
        $this->assertResponseOk();
        // 断言是否为 Response 对象
        if ($this->response instanceof \think\Response) {
            $this->responseEquals();
        }
        // 是否是$this->error错误页
        $this->isErrorContent();
        // 断言接口
        $this->checkResponseData();
    }

    /**
     * 获取api必要参数
     * @param int $appId 接口秘钥app_id
     * @return array
     */
    protected function apiParameters(int $appId)
    {
        $parameters = [];
        $parameters['request_time'] = time();
        $parameters['app_id'] = $appId;
        return $parameters;
    }

    /**
     * 计算sign
     * @param int $appId 接口秘钥app_id
     * @param array $params 参数
     * @return string
     * @throws Exception
     */
    private function signature(int $appId, array $params): string
    {
        $apiInterface = App::make(ApiInterface::class);
        try {
            $serverSign = $apiInterface->generateServerSign($appId, $params);
        } catch (Exception $e) {
            $this->output->error($e->getMessage());
            throw $e;
        }
        return $serverSign;
    }

    /**
     * @param string $uri
     * @param array $data
     * @param array $headers
     * @param array $files
     * @return $this
     */
    public function post($uri, array $data = [], array $headers = [], array $files = [])
    {
        $server = $this->transformHeadersToServerVars($headers);
        $this->call('POST', $uri, $data, [], $files, $server);
        return $this;
    }

    /**
     * 验证相应对象
     * @return void
     */
    public function checkResponseData()
    {
        $this->isJsonRetrun();
        $responseData = $this->response->getData();
        $this->assertArrayHasKey('status', $responseData);
        $this->assertEquals(1, $responseData['status'], "status err. {$responseData['msg']}");
        $this->assertArrayHasKey('error_code', $responseData);
        $this->assertEquals(0, $responseData['error_code'], "error_code err. {$responseData['msg']}");
        $this->assertArrayHasKey('data', $responseData);
    }
}
