<?php
// +----------------------------------------------------------------------
// | rpc测试基类
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace tests\rpc;

use core\traits\ErrorClass;
use tests\connector\TestBase;
use tests\traits\ValidationTraits;

class Rpc extends TestBase
{
    use ValidationTraits;
    use ErrorClass;

    /**
     * rpc加载状态
     * @var bool
     */
    protected static bool $loadRpc = true;

    /**
     * rpc类文件
     * @var string
     */
    protected string $rpcClass;

    /**
     * rpc对象
     * @var object
     */
    protected object $rpc;

    /**
     * 初始化
     * @return void
     */
    protected function setUp(): void
    {
        parent::setUp();
        $this->rpc = new $this->rpcClass();
    }
}
