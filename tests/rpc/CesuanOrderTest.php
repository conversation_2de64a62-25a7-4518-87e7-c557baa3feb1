<?php
// +----------------------------------------------------------------------
// | CesuanOrderTest
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace tests\rpc;

use app\model\cesuan\Order;
use app\model\user\User as UserModel;
use app\rpc\CesuanOrder;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;

class CesuanOrderTest extends Rpc
{

    /**
     * rpc类
     * @var string
     */
    protected string $rpcClass = CesuanOrder::class;

    /**
     * 同步更新(不存在会新增)
     * @return void
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function testSync()
    {
        $uid = UserModel::order(['id' => 'desc'])->value('id');
        $order = Order::order(['id' => 'desc'])->find();

        $data = $order->toArray();
        $data['order_sn'] = 'RD' . mt_rand(100000, 999999);
        $data['order_id'] = mt_rand(100000, 999999);
        unset($data['id'], $data['uid']);

        $ref = $this->rpc->sync($uid, $data);
        $this->assertTrue($ref, '同步失败');

        $order2 = Order::where('order_sn', $data['order_sn'])->find();

        $this->assertTrue(
            $this->validation($order2->toArray(), [
                'order_sn' => ['require', "in:{$data['order_sn']}"],
            ]),
            '数据验证未通过：' . $this->getError()
        );
    }
}
