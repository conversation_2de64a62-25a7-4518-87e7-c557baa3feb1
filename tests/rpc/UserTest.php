<?php
// +----------------------------------------------------------------------
// | UserTest
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace tests\rpc;

use app\model\user\User as UserModel;
use app\rpc\User;
use think\facade\Cache;

class UserTest extends Rpc
{

    /**
     * rpc类
     * @var string
     */
    protected string $rpcClass = User::class;

    /**
     * 用户信息
     * @return void
     */
    public function testInfo()
    {
        $uid = UserModel::order(['id' => 'desc'])->value('id');
        $info = $this->rpc->info($uid);
        $this->assertTrue(
            $this->validation($info, [
                'id' => ['require'],
                'username' => ['require'],
                'nickname' => ['require'],
                'mobile' => ['require'],
            ]),
            '数据验证未通过：' . $this->getError()
        );
    }

    /**
     * 复用手机号登录验证码
     * @return void
     */
    public function testReuseMobileLoginVerificationCode()
    {
        $mobile = '15888888888';
        $code = mt_rand(100000, 999999);
        $this->rpc->reuseMobileLoginVerificationCode($mobile, $code);
        $key = User::getverificationCodeKey($mobile);
        $this->assertTrue(Cache::get($key) == $code);
    }
}
