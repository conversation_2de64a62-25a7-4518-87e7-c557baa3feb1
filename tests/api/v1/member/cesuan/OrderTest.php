<?php
// +----------------------------------------------------------------------
// | OrderTest
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace tests\api\v1\member\cesuan;

use pass\exception\AccessTokenException;
use tests\connector\ApiMemberBase;

class OrderTest extends ApiMemberBase
{
    /**
     * 接口
     * @var string
     */
    protected string $commonPath = '/qyapp/v1/member.cesuan.order';

    /**
     * 列表
     * @return void
     * @throws AccessTokenException
     */
    public function testList()
    {
        $query = [
            'page' => 1,
            'limit' => 2,
        ];
        return $this->baseListTest(
            $this->commonPath . '/list.html',
            $query,
            [
                'data.count' => ['require', 'number'],
                'data.data' => ['array'],
                'data.data.0.server_url' => ['requireWith:data.data.0', 'require'],
                'data.data.0.id' => ['requireWith:data.data.0', 'require', 'number'],
                'data.data.0.uid' => ['requireWith:data.data.0', 'require', 'number'],
                'data.data.0.order_sn' => ['requireWith:data.data.0', 'require'],
                'data.data.0.status' => ['requireWith:data.data.0', 'require'],
                'data.data.0.servers_id' => ['requireWith:data.data.0', 'require'],
                'data.data.0.servers_name' => ['requireWith:data.data.0', 'require'],
                'data.data.0.servers_ico' => ['requireWith:data.data.0', 'require', 'url'],
                'data.data.0.info' => ['requireWith:data.data.0', 'require', 'array'],
                'data.data.0.delete_time' => ['requireWith:data.data.0', 'require', 'number'],
                'data.data.0.update_time' => ['requireWith:data.data.0', 'require', 'number'],
                'data.data.0.create_time' => ['requireWith:data.data.0', 'require', 'number'],
            ],
        );

    }
}
