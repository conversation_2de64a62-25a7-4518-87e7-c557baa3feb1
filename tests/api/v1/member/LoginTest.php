<?php
// +----------------------------------------------------------------------
// | LoginTest
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace tests\api\v1\member;

use pass\exception\AccessTokenException;
use tests\connector\ApiBase;

class LoginTest extends ApiBase
{
    /**
     * 测试昵称是否存在
     * @return void
     * @throws AccessTokenException
     */
    public function testNicknameExist()
    {
        $url = '/qyapp/v1/member.login/nicknameExist.html';

        $params = [
            'nickname' => 'test_nickname_' . time(),
        ];
        $this->pageGet($url . '?' . http_build_query($params));
        $result = $this->response->getData();

        $this->assertEquals(1, $result['status'], '昵称不存在应该返回成功');
        $this->assertTrue($result['data'], '返回数据不符合预期');
    }

    /**
     * 测试邮箱是否存在
     * @return void
     * @throws AccessTokenException
     */
    public function testEmailExist()
    {
        $url = '/qyapp/v1/member.login/emailExist.html';

        $params = [
            'email' => 'test_' . time() . '@example.com',
        ];
        $this->pageGet($url . '?' . http_build_query($params));
        $result = $this->response->getData();

        $this->assertEquals(1, $result['status'], '邮箱不存在应该返回成功');
        $this->assertTrue($result['data'], '返回数据不符合预期');
    }

    /**
     * 测试手机验证码登录
     * @return void
     * @throws AccessTokenException
     */
    public function testMobileLogin()
    {
        $url = '/qyapp/v1/member.login/mobilelogin.html';

        $params = [
            'devicetype' => 'ios_app',
            'mobile' => '13800002023',
            'captcha' => '080808',
        ];
        $this->pagePost($url, $params);
        $result = $this->response->getData();

        $this->assertEquals(1, $result['status'], '使用测试账号登录失败：' . ($result['msg'] ?? '未知错误'));
        $this->assertArrayHasKey('token', $result['data'], '返回数据缺少token字段');
        $this->assertArrayHasKey('expires_in', $result['data'], '返回数据缺少expires_in字段');
    }

    /**
     * 测试发送验证码
     * @return void
     * @throws AccessTokenException
     */
    public function testSendVerificationCode()
    {
        $url = '/qyapp/v1/member.login/sendVerificationCode.html';

        // 测试情况: 发送验证码到测试手机号
        $params = [
            'phone' => '13800002023',
        ];
        $this->pagePost($url, $params);
        $result = $this->response->getData();

        $this->assertEquals(1, $result['status'], '发送验证码失败：' . ($result['msg'] ?? '未知错误'));
        $this->assertTrue($result['data'], '返回数据不符合预期');
    }
}
