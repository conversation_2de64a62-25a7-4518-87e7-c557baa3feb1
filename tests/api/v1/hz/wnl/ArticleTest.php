<?php
// +----------------------------------------------------------------------
// | ArticleTest
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace tests\api\v1\hz\wnl;

use app\model\hz\wnl\Article;
use pass\exception\AccessTokenException;
use tests\connector\ApiBase;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\Exception;

class ArticleTest extends ApiBase
{
    protected string $commonPath = '/qyapp/v1/hz.wnl.article';

    /**
     * 列表
     * @return void
     * @throws AccessTokenException
     */
    public function testList()
    {
        $query = [
            'page' => 1,
            'limit' => 2,
        ];
        return $this->baseListTest(
            $this->commonPath . '/list.html',
            $query,
            [
                'data' => ['array'],
                'data.0.category' => ['requireWith:data.0', 'require'],
                'data.0.title' => ['requireWith:data.0', 'require'],
                'data.0.covertype' => ['requireWith:data.0', 'require'],
                'data.0.creationtime' => ['requireWith:data.0', 'require', 'dateFormat:Y-m-d H:i:s'],
                'data.0.author' => ['requireWith:data.0', 'require'],
                'data.0.source' => ['requireWith:data.0', 'require'],
                'data.0.description' => ['requireWith:data.0', 'require'],
            ],
        );
    }

    /**
     * 详情
     * @return void
     * @throws AccessTokenException
     * @throws Exception
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function testInfo()
    {
        $url = $this->commonPath . '/info.html';
        $this->baseInfoTest(Article::orderRand()->find(), $url, [
            'data' => ['array'],
            'data.id' => ['require'],
            'data.title' => ['require'],
            'data.category' => ['require'],
        ]);
    }
}
