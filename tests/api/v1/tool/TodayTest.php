<?php
// +----------------------------------------------------------------------
// | TodayTest
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace tests\api\v1\tool;

use pass\exception\AccessTokenException;
use tests\connector\ApiBase;

class TodayTest extends ApiBase
{
    /**
     * 测试历史上的今天列表
     * @return void
     * @throws AccessTokenException
     */
    public function testList()
    {
        $url = '/qyapp/v1/tool.today/list.html';

        $this->pageGet($url);
        $result = $this->response->getData();

        $this->assertEquals(1, $result['status'], '获取默认列表失败：' . ($result['msg'] ?? '未知错误'));
        $this->assertArrayHasKey('data', $result, '返回数据结构不符合预期');

        // 验证返回的数据是否符合预期
        if (!empty($result['data']['data'])) {
            $item = $result['data']['data'][0];
            $this->assertArrayHasKey('id', $item, '返回数据项缺少id字段');
            $this->assertArrayHasKey('month', $item, '返回数据项缺少month字段');
            $this->assertArrayHasKey('day', $item, '返回数据项缺少day字段');
            $this->assertArrayHasKey('date', $item, '返回数据项缺少date字段');
            $this->assertArrayHasKey('title', $item, '返回数据项缺少title字段');
            $this->assertArrayHasKey('create_time', $item, '返回数据项缺少create_time字段');
            $this->assertArrayHasKey('update_time', $item, '返回数据项缺少update_time字段');

            // 验证是否int
            $this->assertIsInt($item['create_time'], '返回数据create_time字段非int');
            $this->assertIsInt($item['update_time'], '返回数据update_time字段非int');

            // 验证月日是否为默认值1
            $this->assertEquals(1, $item['month'], '默认月份应为1');
            $this->assertEquals(1, $item['day'], '默认日期应为1');
        }
    }
}
