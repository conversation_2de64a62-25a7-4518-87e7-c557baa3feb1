<?php
// +----------------------------------------------------------------------
// | UpgradeTest
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace tests\api\v1\tool;

use pass\exception\AccessTokenException;
use tests\connector\ApiBase;

class UpgradeTest extends ApiBase
{
    /**
     * 测试应用更新检测
     * @return void
     * @throws AccessTokenException
     */
    public function testCheckUpdate()
    {
        $url = '/qyapp/v1/tool.upgrade/checkUpdate.html';

        $params = [
            'app_version' => '1.0.0',
            'devicetype' => 'android_app',
        ];
        $this->pageGet($url . '?' . http_build_query($params));
        $result = $this->response->getData();

        // 如果有新版本，status应该为1
        if ($result['status'] == 1) {
            $this->assertArrayHasKey('id', $result['data'], '返回数据缺少id字段');
            $this->assertArrayHasKey('version', $result['data'], '返回数据缺少version字段');
            $this->assertArrayHasKey('version_int', $result['data'], '返回数据缺少version_int字段');
            $this->assertArrayHasKey('platform', $result['data'], '返回数据缺少platform字段');
            $this->assertArrayHasKey('url', $result['data'], '返回数据缺少url字段');

            // 验证版本号是否大于当前版本
            $this->assertGreaterThan(1000000, $result['data']['version_int'], '新版本号应该大于当前版本');
            // 验证平台是否包含Android(1)
            $this->assertStringContainsString('1', $result['data']['platform'], '平台应该包含Android');
        } else {
            // 如果没有新版本，应该返回特定的错误信息
            $this->assertEquals(0, $result['status'], '无新版本时状态码应为0');
            $this->assertStringContainsString('没有新版本', $result['msg'], '错误信息不符合预期');
            $this->assertArrayHasKey('app_version', $result['data'], '返回数据缺少app_version字段');
            $this->assertArrayHasKey('new_version', $result['data'], '返回数据缺少new_version字段');
        }
    }
}
