<?php
// +----------------------------------------------------------------------
// | FeedbackTest
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace tests\api\v1\tool;

use pass\exception\AccessTokenException;
use tests\connector\ApiBase;

class FeedbackTest extends ApiBase
{
    /**
     * 测试提交反馈
     * @return void
     * @throws AccessTokenException
     */
    public function testSubmit()
    {
        $url = '/qyapp/v1/tool.feedback/submit.html';

        $params = [
            'content' => '这是一条测试反馈内容',
            'pic' => [
                [
                    'url' => 'https://example.com/image1.jpg',
                    'title' => '测试图片1',
                ],
                [
                    'url' => 'https://example.com/image2.jpg',
                    'title' => '测试图片2',
                ],
            ],
        ];

        $this->pagePost($url, $params);
        $result = $this->response->getData();

        $this->assertEquals(1, $result['status'], '提交带图片的反馈失败：' . ($result['msg'] ?? '未知错误'));
        $this->assertTrue(isset($result['data']), '返回数据不符合预期');
    }
}
