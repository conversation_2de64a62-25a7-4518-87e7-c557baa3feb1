<?php
// +----------------------------------------------------------------------
// | JmTest
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace tests\api\v1\adm\tool;

use app\model\tool\Jm;
use pass\exception\AccessTokenException;
use tests\connector\ApiAdmBase;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\Exception;

class JmTest extends ApiAdmBase
{
    /**
     * @var string
     */
    protected string $commonPath = '/qyapp/v1/adm.tool.jm/';

    /**
     * 栏目分类
     * @return void
     * @throws AccessTokenException
     */
    public function testCategory()
    {
        $url = $this->commonPath . 'category.html';
        $this->baseListTest($url, [], [
            'data' => ['require', 'array'],
        ]);
    }

    /**
     * 详情
     * @return void
     * @throws AccessTokenException
     * @throws Exception
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function testInfo()
    {
        $url = $this->commonPath . 'info.html';
        $info = Jm::orderRand()->find();
        $this->baseInfoTest($info, $url, [
            'data' => ['require', 'array'],
            'data.id' => ['require', 'number'],
            'data.title' => ['require'],
            'data.category' => ['require', 'number'],
            'data.content' => ['require'],
        ]);
    }

    /**
     * 列表
     * @return void
     * @throws AccessTokenException
     */
    public function testList()
    {
        $url = $this->commonPath . 'list.html';
        $param = [
            'keyword' => '',
            'status' => rand(0, 1),
            'category' => rand(0, 15),
            'time_type' => '',
            'start_time' => '',
            'end_time' => '',
        ];
        $this->baseListTest($url, $param, [
            'data' => ['require'],
            'data.data' => ['array'],
            'data.count' => ['number'],
            'data.data.0.id' => ['requireWith:data.data.0', 'number'],
            'data.data.0.title' => ['requireWith:data.data.0'],
            'data.data.0.category' => ['requireWith:data.data.0', 'number'],
            'data.data.0.content' => ['requireWith:data.data.0'],
        ]);
    }

    /**
     * 添加
     * @return mixed
     * @throws AccessTokenException
     * @throws DataNotFoundException
     * @throws DbException
     * @throws Exception
     * @throws ModelNotFoundException
     */
    public function testAdd()
    {
        $url = $this->commonPath . 'add.html';
        $param = [
            'title' => '单元测试' . rand(1, 10000),
            'category' => rand(1, 15),
            'status' => rand(0, 1),
            'keyword' => '单元测试,测试,单元',
            'thumb' => 'https://img.qywnl.com/wnl/2025/07/686b6181bcf10.jpg',
            'author' => '乾元日历',
            'source' => '互联网',
            'description' => '简介咯',
            'hits' => rand(1, 10000),
            'release_time' => time(),
            'content' => '内容' . rand(1, 10000),
        ];
        return $this->baseAddTest(Jm::class, $url, $param);
    }

    /**
     * 编辑
     * @depends testAdd
     * @param Jm $info
     * @return void
     * @throws AccessTokenException
     * @throws Exception
     */
    public function testEdit(Jm $info)
    {
        $url = $this->commonPath . 'edit.html';
        $param = [
            'id' => $info->id,
            'title' => '单元测试2，' . rand(1, 10000),
            'content' => '内容2，' . rand(1, 10000),
        ];
        $this->baseEditTest($info, $url, $param);
    }

    /**
     * 删除
     * @depends testAdd
     * @param Jm $info
     * @return void
     * @throws AccessTokenException
     * @throws DataNotFoundException
     * @throws DbException
     * @throws Exception
     * @throws ModelNotFoundException
     */
    public function testDelete(Jm $info)
    {
        $url = $this->commonPath . 'delete.html';
        $this->baseDeleteTest($info, $url);
    }
}
