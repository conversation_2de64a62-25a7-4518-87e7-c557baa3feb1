<?php
// +----------------------------------------------------------------------
// | ProtocolTest
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace tests\api\v1\adm\tool;

use app\model\tool\Protocol;
use pass\exception\AccessTokenException;
use tests\connector\ApiAdmBase;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\Exception;
use think\helper\Str;

class ProtocolTest extends ApiAdmBase
{
    /**
     * @var string
     */
    protected string $commonPath = '/qyapp/v1/adm.tool.protocol/';

    /**
     * 列表
     * @return void
     * @throws AccessTokenException
     */
    public function testList()
    {
        $url = $this->commonPath . 'list.html';
        $param = [
            'title' => '',
            'at' => '',
        ];
        $this->baseListTest($url, $param, [
            'data' => ['require'],
            'data.data' => ['array'],
            'data.count' => ['number'],
            'data.data.0.id' => ['requireWith:data.data.0', 'number'],
            'data.data.0.title' => ['requireWith:data.data.0'],
            'data.data.0.at' => ['requireWith:data.data.0'],
            'data.data.0.content' => ['requireWith:data.data.0'],
        ]);
    }

    /**
     * 详情
     * @return void
     * @throws AccessTokenException
     * @throws Exception
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function testInfo()
    {
        $url = $this->commonPath . 'info.html';
        $info = Protocol::orderRand()->find();
        $this->baseInfoTest($info, $url, [
            'data' => ['require', 'array'],
            'data.id' => ['require', 'number'],
            'data.title' => ['require'],
            'data.at' => ['require'],
            'data.content' => ['require'],
        ]);
    }

    /**
     * 添加
     * @return mixed
     * @throws AccessTokenException
     * @throws DataNotFoundException
     * @throws DbException
     * @throws Exception
     * @throws ModelNotFoundException
     */
    public function testAdd()
    {
        $url = $this->commonPath . 'add.html';
        $param = [
            'title' => '单元测试' . rand(1, 10000),
            'at' => Str::random(4),
            'version_str' => date('Y-m-d'),
            'content' => '单元测试,测试,单元',
            'implement_time' => date('Y-m-d H:i:s'),
        ];
        return $this->baseAddTest(Protocol::class, $url, $param);
    }

    /**
     * 编辑
     * @depends testAdd
     * @param Protocol $info
     * @return void
     * @throws AccessTokenException
     * @throws Exception
     */
    public function testEdit(Protocol $info)
    {
        $url = $this->commonPath . 'edit.html';
        $param = [
            'id' => $info->id,
            'title' => '单元测试2，' . rand(1, 10000),
            'at' => $info->at,
            'version_str' => $info->version_str,
            'content' => '内容2，' . rand(1, 10000),
        ];
        $this->baseEditTest($info, $url, $param);
    }

    /**
     * 删除
     * @depends testAdd
     * @param Protocol $info
     * @return void
     * @throws AccessTokenException
     * @throws DataNotFoundException
     * @throws DbException
     * @throws Exception
     * @throws ModelNotFoundException
     */
    public function testDelete(Protocol $info)
    {
        $url = $this->commonPath . 'delete.html';
        $this->baseDeleteTest($info, $url);
    }
}
