<?php
// +----------------------------------------------------------------------
// | UpgradeTest
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace tests\api\v1\adm\tool;

use app\model\tool\Upgrade;
use pass\exception\AccessTokenException;
use tests\connector\ApiAdmBase;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\Exception;

class UpgradeTest extends ApiAdmBase
{
    /**
     * @var string
     */
    protected string $commonPath = '/qyapp/v1/adm.tool.upgrade/';

    /**
     * 列表
     * @return void
     * @throws AccessTokenException
     */
    public function testList()
    {
        $url = $this->commonPath . 'list.html';
        $param = [
            'status' => '',
            'platform' => '',
        ];
        $this->baseListTest($url, $param, [
            'data' => ['require'],
            'data.data' => ['array'],
            'data.count' => ['number'],
            'data.data.0.id' => ['requireWith:data.data.0', 'number'],
            'data.data.0.title' => ['requireWith:data.data.0'],
            'data.data.0.update_content' => ['requireWith:data.data.0'],
            'data.data.0.version' => ['requireWith:data.data.0'],
        ]);
    }

    /**
     * 详情
     * @return void
     * @throws AccessTokenException
     * @throws Exception
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function testInfo()
    {
        $url = $this->commonPath . 'info.html';
        $info = Upgrade::orderRand()->find();
        $this->baseInfoTest($info, $url, [
            'data' => ['require', 'array'],
            'data.id' => ['require', 'number'],
            'data.title' => ['require'],
            'data.update_content' => ['require'],
            'data.version' => ['require'],
        ]);
    }

    /**
     * 添加
     * @return mixed
     * @throws AccessTokenException
     * @throws DataNotFoundException
     * @throws DbException
     * @throws Exception
     * @throws ModelNotFoundException
     */
    public function testAdd()
    {
        $url = $this->commonPath . 'add.html';
        $param = [
            'title' => '单元测试' . rand(1, 10000),
            'status' => rand(0, 1),
            'update_method' => '1',
            'update_force' => '0',
            'update_content' => '撒大声地',
            'platform' => '1',
            'version_str' => '1.1.0',
            'min_version' => '',
            'url' => 'https://www.baidu.com',
        ];
        return $this->baseAddTest(Upgrade::class, $url, $param);
    }

    /**
     * 编辑
     * @depends testAdd
     * @param Upgrade $info
     * @return void
     * @throws AccessTokenException
     * @throws Exception
     */
    public function testEdit(Upgrade $info)
    {
        $url = $this->commonPath . 'edit.html';
        $param = [
            'id' => $info->id,
            'update_content' => '内容2，' . rand(1, 10000),
        ];
        $this->baseEditTest($info, $url, $param);
    }

    /**
     * 删除
     * @depends testAdd
     * @param Upgrade $info
     * @return void
     * @throws AccessTokenException
     * @throws DataNotFoundException
     * @throws DbException
     * @throws Exception
     * @throws ModelNotFoundException
     */
    public function testDelete(Upgrade $info)
    {
        $url = $this->commonPath . 'delete.html';
        $this->baseDeleteTest($info, $url);
    }
}
