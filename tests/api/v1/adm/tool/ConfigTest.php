<?php
// +----------------------------------------------------------------------
// | ConfigTest
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace tests\api\v1\adm\tool;

use app\model\system\Config;
use pass\exception\AccessTokenException;
use tests\connector\ApiAdmBase;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;

class ConfigTest extends ApiAdmBase
{
    /**
     * @var string
     */
    protected string $commonPath = '/qyapp/v1/adm.tool.config/';

    /**
     * 保存配置
     * @return void
     * @throws AccessTokenException
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function testSave()
    {
        $url = $this->commonPath . 'save.html';
        $appBeian = '闽ICP备2025087419号-3A' . time();
        $param = [
            'app_beian' => json_encode($appBeian),
        ];

        $this->pagePost($url, $param);
        $this->assertNotEmpty($this->response->getData()['data'] ?? false, '配置保存失败');

        $config = Config::info('app_beian');
        $this->assertEquals($appBeian, $config->config(), '配置保存失败');
    }
}
