<?php
// +----------------------------------------------------------------------
// | FeedbackTest
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace tests\api\v1\adm\tool;

use pass\exception\AccessTokenException;
use tests\connector\ApiAdmBase;

class FeedbackTest extends ApiAdmBase
{
    /**
     * @var string
     */
    protected string $commonPath = '/qyapp/v1/adm.tool.feedback/';

    /**
     * 列表
     * @return void
     * @throws AccessTokenException
     */
    public function testList()
    {
        $url = $this->commonPath . 'list.html';
        $param = [
            'status' => rand(0, 1),
        ];
        $this->baseListTest($url, $param, [
            'data' => ['require'],
            'data.data' => ['array'],
            'data.count' => ['number'],
            'data.data.0.id' => ['requireWith:data.data.0', 'number'],
            'data.data.0.status' => ['number', 'in:0,1'],
            'data.data.0.uid' => ['number'],
            'data.data.0.content' => ['requireWith:data.data.0'],
            'data.data.0.pic' => ['array'],
            'data.data.0.video' => ['array'],
            'data.data.0.create_time' => ['requireWith:data.data.0', 'number'],
            'data.data.0.update_time' => ['requireWith:data.data.0', 'number'],
        ]);
    }
}
