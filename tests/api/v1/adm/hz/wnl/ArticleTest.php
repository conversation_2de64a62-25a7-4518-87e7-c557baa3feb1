<?php
// +----------------------------------------------------------------------
// | ArticleTest - 万年历文章管理单元测试
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace tests\api\v1\adm\hz\wnl;

use app\model\hz\wnl\Article;
use pass\exception\AccessTokenException;
use tests\connector\ApiAdmBase;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\Exception;

class ArticleTest extends ApiAdmBase
{
    /**
     * @var string
     */
    protected string $commonPath = '/qyapp/v1/adm.hz.wnl.article/';

    /**
     * 详情
     * @return void
     * @throws AccessTokenException
     * @throws Exception
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function testInfo()
    {
        $url = $this->commonPath . 'info.html';
        $info = Article::orderRand()->find();
        $this->baseInfoTest($info, $url, [
            'data' => ['require', 'array'],
            'data.id' => ['require', 'number'],
            'data.title' => ['require'],
            'data.category' => ['require', 'number'],
            'data.status' => ['require', 'number'],
            'data.author' => ['require'],
            'data.source' => ['require'],
            'data.content' => ['require'],
            'data.category_name' => ['require'],
        ]);
    }

    /**
     * 列表
     * @return void
     * @throws AccessTokenException
     */
    public function testList()
    {
        $url = $this->commonPath . 'list.html';
        $param = [
            'status' => rand(0, 1),
            'title' => '',
            'category' => rand(1, 3),
        ];
        $this->baseListTest($url, $param, [
            'data' => ['require'],
            'data.data' => ['array'],
            'data.count' => ['number'],
            'data.data.0.id' => ['requireWith:data.data.0', 'number'],
            'data.data.0.title' => ['requireWith:data.data.0'],
            'data.data.0.category' => ['requireWith:data.data.0', 'number'],
            'data.data.0.status' => ['requireWith:data.data.0', 'number'],
            'data.data.0.author' => ['requireWith:data.data.0'],
            'data.data.0.source' => ['requireWith:data.data.0'],
            'data.data.0.content' => ['requireWith:data.data.0'],
            'data.data.0.category_name' => ['requireWith:data.data.0'],
        ]);
    }

    /**
     * 添加
     * @return mixed
     * @throws AccessTokenException
     * @throws DataNotFoundException
     * @throws DbException
     * @throws Exception
     * @throws ModelNotFoundException
     */
    public function testAdd()
    {
        $url = $this->commonPath . 'add.html';
        $param = [
            'category' => rand(1, 3),
            'title' => '单元测试文章标题' . rand(1, 10000),
            'status' => rand(0, 1),
            'coverphoto' => 'https://img.qywnl.com/wnl/2025/07/686b6181bcf10.jpg',
            'author' => '乾元日历',
            'source' => '乾元日历',
            'content' => '这是单元测试文章内容，用于测试文章添加功能。内容长度需要满足验证要求。' . rand(1, 10000),
            'release_time' => date('Y-m-d H:i:s'),
        ];
        return $this->baseAddTest(Article::class, $url, $param);
    }

    /**
     * 编辑
     * @depends testAdd
     * @param Article $info
     * @return void
     * @throws AccessTokenException
     * @throws Exception
     */
    public function testEdit(Article $info)
    {
        $url = $this->commonPath . 'edit.html';
        $param = [
            'id' => $info->id,
            'title' => '单元测试编辑文章标题' . rand(1, 10000),
            'content' => '这是编辑后的单元测试文章内容，用于测试文章编辑功能。' . rand(1, 10000),
            'author' => '测试作者',
            'status' => 1,
        ];
        $this->baseEditTest($info, $url, $param);
    }

    /**
     * 删除
     * @depends testAdd
     * @param Article $info
     * @return void
     * @throws AccessTokenException
     * @throws DataNotFoundException
     * @throws DbException
     * @throws Exception
     * @throws ModelNotFoundException
     */
    public function testDelete(Article $info)
    {
        $url = $this->commonPath . 'delete.html';
        $query = [
            'id' => $info->id,
        ];
        $this->pageGet($url . '?' . http_build_query($query));

        $this->assertTrue($this->response->getData()['data'] ?? false);
        $this->assertEmpty($info->find($info->id), '删除测试用例失败' . $info->getError());
    }
}
