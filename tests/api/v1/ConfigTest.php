<?php
// +----------------------------------------------------------------------
// | ConfigTest
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace tests\api\v1;

use pass\exception\AccessTokenException;
use tests\connector\ApiBase;

class ConfigTest extends ApiBase
{

    /**
     * 获取全部配置
     * @return void
     * @throws AccessTokenException
     */
    public function testAll()
    {
        $url = '/qyapp/v1/config/all.html';
        $this->pageGet($url);
        $result = $this->response->getData()['data'];
        $this->assertTrue(
            $this->validation($result, [
                'customer_wx' => ['require', 'array'],
            ]),
            '数据验证未通过：' . $this->getError()
        );
    }
}
