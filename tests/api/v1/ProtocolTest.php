<?php
// +----------------------------------------------------------------------
// | ProtocolTest
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace tests\api\v1;

use pass\exception\AccessTokenException;
use tests\connector\ApiBase;

class ProtocolTest extends ApiBase
{
    /**
     * 获取全部协议
     * @return void
     * @throws AccessTokenException
     */
    public function testAll()
    {
        $url = '/qyapp/v1/protocol/all.html';
        $this->pageGet($url);
        $result = $this->response->getData()['data'];
        $this->assertTrue(
            $this->validation($result, [
                'yhxy' => ['require'],
                'ysxy' => ['require'],
            ]),
            '数据验证未通过：' . $this->getError()
        );

        // 验证协议内容结构
        $this->assertArrayHasKey('title', $result['yhxy'], '用户协议缺少title字段');
        $this->assertArrayHasKey('content', $result['yhxy'], '用户协议缺少content字段');
        $this->assertArrayHasKey('title', $result['ysxy'], '隐私协议缺少title字段');
        $this->assertArrayHasKey('content', $result['ysxy'], '隐私协议缺少content字段');

        // 验证协议标题
        $this->assertEquals('用户协议', $result['yhxy']['title'], '用户协议标题不匹配');
        $this->assertEquals('隐私协议', $result['ysxy']['title'], '隐私协议标题不匹配');

        // 验证协议内容不为空
        $this->assertNotEmpty($result['yhxy']['content'], '用户协议内容为空');
        $this->assertNotEmpty($result['ysxy']['content'], '隐私协议内容为空');
    }
}
